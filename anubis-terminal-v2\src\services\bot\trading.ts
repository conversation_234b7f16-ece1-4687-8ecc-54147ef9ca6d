import { api } from './index';

// Trading Types
export interface TradeParams {
  token: string;
  amount: number;
  orderType?: 'market' | 'limit';
  limitPrice?: number;
  slippage?: number;
}

export interface SellParams {
  token: string;
  percent: number;
  orderType?: 'market' | 'limit';
  limitPrice?: number;
}

export interface SnipeParams {
  token: string;
  amount: number;
}

export interface Order {
  id: string;
  type: 'buy' | 'sell';
  orderType: 'market' | 'limit';
  token: string;
  tokenSymbol?: string;
  tokenName?: string;
  amount: number;
  price?: number;
  status: 'pending' | 'filled' | 'cancelled' | 'failed';
  timestamp: number;
  txHash?: string;
  chainId: number;
}

export interface Position {
  id: string;
  token: string;
  tokenSymbol: string;
  tokenName: string;
  amount: string;
  entryPrice: number;
  currentPrice: number;
  pnl: number;
  pnlPercent: number;
  timestamp: number;
  chainId: number;
}

export interface TradeResponse {
  success: boolean;
  message: string;
  token?: string;
  amount?: number;
  txHash?: string;
  orderId?: string;
}

export interface MarketData {
  address: string;
  name: string | null;
  symbol: string | null;
  balance: string;
  priceUsd: number | null;
  priceNative: number | null;
  fdv: number | null;
  liquidity: number | null;
  volume24h: number | null;
  priceChange24h: number | null;
  url: string | null;
}

export interface TradingFees {
  gasFee: string;
  tradingFee: string;
  slippageFee: string;
  totalFee: string;
}

export interface OrderHistory {
  orders: Order[];
  total: number;
  hasMore: boolean;
}

/**
 * Execute a buy trade
 */
export const executeTrade = async (
  token: string,
  params: TradeParams
): Promise<TradeResponse> => {
  try {
    const response = await api.post('/trade/token', {
      token: params.token,
      amount: params.amount,
      orderType: params.orderType || 'market',
      limitPrice: params.limitPrice,
      slippage: params.slippage
    }, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return response.data;
  } catch (error) {
    console.error('Error executing trade:', error);
    throw new Error('Failed to execute trade');
  }
};

/**
 * Execute a snipe trade
 */
export const executeSnipe = async (
  token: string,
  params: TradeParams
): Promise<TradeResponse> => {
  try {
    const response = await api.post('/trade/snipe', {
      token: params.token,
      amount: params.amount
    }, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return response.data;
  } catch (error) {
    console.error('Error executing snipe:', error);
    throw new Error('Failed to execute snipe');
  }
};

/**
 * Execute a sell trade
 */
export const executeSell = async (
  token: string,
  params: SellParams
): Promise<TradeResponse> => {
  try {
    const response = await api.post('/trade/sell', {
      token: params.token,
      percent: params.percent,
      orderType: params.orderType || 'market',
      limitPrice: params.limitPrice
    }, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return response.data;
  } catch (error) {
    console.error('Error executing sell:', error);
    throw new Error('Failed to execute sell');
  }
};

/**
 * Get open orders
 */
export const getOpenOrders = async (token: string): Promise<Order[]> => {
  try {
    const response = await api.get('/trade/orders/open', {
      headers: { Authorization: `Bearer ${token}` },
    });
    return response.data.orders || [];
  } catch (error) {
    console.error('Error fetching open orders:', error);
    // Return empty array if endpoint doesn't exist yet
    return [];
  }
};

/**
 * Get order history
 */
export const getOrderHistory = async (
  token: string,
  limit: number = 50,
  offset: number = 0
): Promise<OrderHistory> => {
  try {
    const response = await api.get<OrderHistory>('/trade/orders/history', {
      params: { limit, offset },
      headers: { Authorization: `Bearer ${token}` },
    });
    return {
      orders: response.data.orders || [],
      total: response.data.total || 0,
      hasMore: response.data.hasMore || false
    };
  } catch (error) {
    console.error('Error fetching order history:', error);
    // Return empty result if endpoint doesn't exist yet
    return {
      orders: [],
      total: 0,
      hasMore: false
    };
  }
};

/**
 * Get current positions
 */
export const getPositions = async (token: string): Promise<Position[]> => {
  try {
    const response = await api.get('/trade/positions', {
      headers: { Authorization: `Bearer ${token}` },
    });
    return response.data.positions || [];
  } catch (error) {
    console.error('Error fetching positions:', error);
    // Return empty array if endpoint doesn't exist yet
    return [];
  }
};

/**
 * Cancel an open order
 */
export const cancelOrder = async (
  token: string,
  orderId: string
): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await api.delete(`/trade/orders/${orderId}`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return response.data;
  } catch (error) {
    console.error('Error cancelling order:', error);
    throw new Error('Failed to cancel order');
  }
};

/**
 * Get market data for a token
 */
export const getMarketData = async (
  token: string,
  tokenAddress: string,
  chainId: number
): Promise<MarketData> => {
  try {
    const response = await api.get<MarketData>(`/bot/user/token/${tokenAddress}`, {
      params: { chainId },
      headers: { Authorization: `Bearer ${token}` },
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching market data:', error);
    throw new Error('Failed to fetch market data');
  }
};

/**
 * Get trading fees and gas estimates
 */
export const getTradingFees = async (
  token: string,
  tradeParams: TradeParams
): Promise<TradingFees> => {
  try {
    const response = await api.post<TradingFees>('/trade/fees', tradeParams, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching trading fees:', error);
    // Return default values if endpoint doesn't exist yet
    return {
      gasFee: '0.001',
      tradingFee: '0.003',
      slippageFee: '0.001',
      totalFee: '0.005'
    };
  }
};
