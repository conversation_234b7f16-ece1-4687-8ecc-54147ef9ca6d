"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useUserData } from "@/hooks/useUserData";
import { useRouter } from "next/navigation";
import { useEffect, useState, useCallback, useMemo } from "react";
import {
  FiPlus,
  FiUpload,
  FiSearch,
  FiCopy,
  FiExternalLink,
  FiTrash2,
  FiEdit2,
  FiCheck,
  FiX,
  FiDownload,
  FiEye,
  FiEyeOff,
  FiShield,
  Fi<PERSON>ser as FiWallet,
  FiTrendingUp,
  FiRefreshCw,
  FiSettings,
  FiStar,
  FiMoreVertical,
  FiArrowUpRight,
  FiArrowDownLeft
} from "react-icons/fi";
import { motion, AnimatePresence } from "framer-motion";
import {
  Wallet,
  createWallet,
  getWallets,
  importWallet,
  setPrimaryWallet,
  getWalletPrivateKey,
  getUserPortfolio,
  searchTokenByContract,
  send2FACode,
  verify2FACode,
  type UserPortfolio,
  type ChainPortfolio,
  type WalletPrivateKeyResponse
} from "@/services/bot";
import { useWeb3 } from "@/contexts/Web3Context";

// Enhanced Wallet interface for UI
interface EnhancedWallet extends Wallet {
  id: string;
  name: string;
  totalBalance: number;
  totalBalanceUSD: number;
  portfolioData?: ChainPortfolio[];
  isLoading?: boolean;
  lastUpdated?: number;
}

// Modal types
type ModalType = 'create' | 'import' | 'export' | '2fa' | 'portfolio' | null;

// 2FA verification state
interface TwoFAState {
  isRequired: boolean;
  isVerifying: boolean;
  code: string;
  error: string | null;
  pendingAction: (() => Promise<void>) | null;
}

// Helper function to format currency
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { duration: 0.3 } },
};

export default function Wallets() {
  const { token } = useAuth();
  const { data: userData } = useUserData(token);
  const { selectedChain } = useWeb3();
  const router = useRouter();

  // Core state
  const [redirecting, setRedirecting] = useState(false);
  const [wallets, setWallets] = useState<EnhancedWallet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // UI state
  const [searchQuery, setSearchQuery] = useState('');
  const [activeModal, setActiveModal] = useState<ModalType>(null);
  const [selectedWallet, setSelectedWallet] = useState<EnhancedWallet | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Form state
  const [privateKey, setPrivateKey] = useState('');
  const [walletName, setWalletName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);

  // 2FA state
  const [twoFAState, setTwoFAState] = useState<TwoFAState>({
    isRequired: false,
    isVerifying: false,
    code: '',
    error: null,
    pendingAction: null
  });

  // Export state
  const [exportedData, setExportedData] = useState<WalletPrivateKeyResponse | null>(null);
  const [showPrivateKey, setShowPrivateKey] = useState(false);

  // Fetch wallets and enrich with portfolio data
  const fetchWallets = useCallback(async () => {
    if (!token) return;
    setLoading(true);
    setError(null);
    try {
      const [walletsResponse, portfolioResponse] = await Promise.all([
        getWallets(token),
        getUserPortfolio(token).catch(() => ({ success: false, data: [] }))
      ]);

      if (Array.isArray(walletsResponse)) {
        const enhancedWallets: EnhancedWallet[] = walletsResponse.map((wallet) => {
          const portfolioData = portfolioResponse.success ? portfolioResponse.data : [];

          // Calculate total balance across all chains
          let totalBalance = 0;
          let totalBalanceUSD = 0;

          portfolioData.forEach((chain) => {
            chain.tokens.forEach((token) => {
              const balance = parseFloat(token.balance) || 0;
              totalBalance += balance;
              if (token.priceUsd) {
                totalBalanceUSD += balance * token.priceUsd;
              }
            });
          });

          return {
            ...wallet,
            id: wallet.address,
            name: `Wallet ${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`,
            totalBalance,
            totalBalanceUSD,
            portfolioData,
            lastUpdated: Date.now(),
            isLoading: false
          };
        });

        setWallets(enhancedWallets);
      } else if (walletsResponse && typeof walletsResponse === 'object' && 'error' in walletsResponse) {
        const errorResponse = walletsResponse as { error: string };
        throw new Error(errorResponse.error);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load wallets';
      setError(`Failed to load wallets: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, [token]);

  // Initial fetch
  useEffect(() => {
    if (token) {
      fetchWallets();
    }
  }, [token, fetchWallets]);

  // Handle redirect if no token
  useEffect(() => {
    if (!token && !redirecting) {
      setRedirecting(true);
      router.replace('/terminal/error');
    }
  }, [token, redirecting, router]);

  // Filter wallets based on search query
  const filteredWallets = useMemo(() => {
    if (!searchQuery.trim()) return wallets;

    const query = searchQuery.toLowerCase().trim();
    return wallets.filter(wallet =>
      wallet.address.toLowerCase().includes(query) ||
      wallet.name.toLowerCase().includes(query)
    );
  }, [wallets, searchQuery]);

  // Handle wallet creation
  const handleCreateWallet = async () => {
    if (!token) return;

    setIsSubmitting(true);
    try {
      const newWallet = await createWallet(token);
      if (newWallet && 'address' in newWallet) {
        await fetchWallets();
        setActiveModal(null);
        setSuccess('Wallet created successfully!');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error('Invalid wallet creation response');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Failed to create wallet:', errorMessage);
      setError(`Failed to create wallet: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle wallet import
  const handleImportWallet = async () => {
    if (!token || !privateKey) return;

    setIsSubmitting(true);
    try {
      const result = await importWallet(token, { privateKey });
      if (result && 'address' in result) {
        await fetchWallets();
        setActiveModal(null);
        setPrivateKey('');
        setWalletName('');
        setSuccess('Wallet imported successfully!');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error('Invalid import response');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Invalid private key';
      console.error('Failed to import wallet:', errorMessage);
      setError(`Failed to import wallet: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle set primary wallet
  const handleSetPrimary = async (walletAddress: `0x${string}`) => {
    if (!token) return;

    try {
      await setPrimaryWallet(token, walletAddress);
      await fetchWallets();
      setSuccess('Primary wallet updated successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Failed to set primary wallet:', errorMessage);
      setError(`Failed to set primary wallet: ${errorMessage}`);
    }
  };

  // Handle copy to clipboard
  const handleCopyAddress = (address: string) => {
    navigator.clipboard.writeText(address).catch(err => {
      console.error('Failed to copy address:', err);
      setError('Failed to copy address to clipboard');
    });
    setCopiedAddress(address);
    setTimeout(() => setCopiedAddress(null), 2000);
  };

  // Handle view on explorer
  const handleViewOnExplorer = (address: string) => {
    try {
      const explorerUrl = selectedChain?.scanUrl || 'https://etherscan.io';
      const url = `${explorerUrl}/address/${address}`;

      window.open(url, '_blank', 'noopener,noreferrer');
    } catch (err) {
      console.error('Failed to open explorer:', err);
      setError('Failed to open blockchain explorer');
    }
  };

  // Handle wallet archive
  const handleArchive = async (walletId: string) => {
    if (!token) return;

    try {
      // TODO: Implement archive wallet API call when available
      console.log('Archive wallet:', walletId);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Optimistic update
      setWallets(prevWallets =>
        prevWallets.filter(wallet => wallet.id !== walletId)
      );

      setSuccess('Wallet archived successfully');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to archive wallet';
      console.error('Failed to archive wallet:', errorMessage);
      setError(`Failed to archive wallet: ${errorMessage}`);
      // Re-fetch to revert optimistic update on error
      fetchWallets();
    }
  };

  // Handle edit wallet name
  const handleEditName = (walletId: string, newName: string) => {
    // Optimistic update
    setWallets(prevWallets =>
      prevWallets.map(wallet =>
        wallet.id === walletId ? { ...wallet, name: newName } : wallet
      )
    );

    // TODO: Implement API call to update wallet name when available
    setSuccess('Wallet name updated successfully');
    setTimeout(() => setSuccess(null), 3000);
  };

  // Handle wallet export with 2FA verification
  const handleExportWallet = async (wallet: EnhancedWallet) => {
    if (!token) return;

    setSelectedWallet(wallet);

    // Define the export action
    const performExport = async () => {
      try {
        setIsSubmitting(true);
        const response = await getWalletPrivateKey(token, wallet.address);
        setExportedData(response);
        setActiveModal('export');
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to export wallet';
        console.error('Failed to export wallet:', errorMessage);
        setError(`Failed to export wallet: ${errorMessage}`);
      } finally {
        setIsSubmitting(false);
      }
    };

    // First request 2FA code
    try {
      await send2FACode(token);
      setTwoFAState({
        isRequired: true,
        isVerifying: false,
        code: '',
        error: null,
        pendingAction: performExport
      });
      setActiveModal('2fa');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send 2FA code';
      setError(`Failed to send 2FA code: ${errorMessage}`);
    }
  };

  // Handler for deposit action
  const handleDeposit = (wallet: EnhancedWallet) => {
    setSelectedWallet(wallet);
    // Show deposit modal or instructions
    // For now, just show the wallet address for manual deposit
    setSuccess(`To deposit, send funds to: ${wallet.address}`);
    setTimeout(() => setSuccess(null), 5000);
  };

  // Handler for withdraw action
  const handleWithdraw = (wallet: EnhancedWallet) => {
    setSelectedWallet(wallet);
    // TODO: Implement withdraw flow with proper UI
    setError('Withdraw functionality will be available in a future update');
    setTimeout(() => setError(null), 3000);
  };

  // Handler for edit action
  const handleEdit = (wallet: EnhancedWallet) => {
    setSelectedWallet(wallet);
    const newName = prompt('Enter new wallet name:', wallet.name);
    if (newName && newName !== wallet.name) {
      handleEditName(wallet.id, newName);
    }
  };

  // Handle 2FA verification
  const handle2FAVerification = async () => {
    if (!token || !twoFAState.code || !twoFAState.pendingAction) return;

    setTwoFAState(prev => ({ ...prev, isVerifying: true, error: null }));

    try {
      const response = await verify2FACode(token, twoFAState.code);

      if (response.success) {
        // Execute the pending action after successful verification
        await twoFAState.pendingAction();

        // Reset 2FA state
        setTwoFAState({
          isRequired: false,
          isVerifying: false,
          code: '',
          error: null,
          pendingAction: null
        });
      } else {
        setTwoFAState(prev => ({
          ...prev,
          isVerifying: false,
          error: 'Invalid verification code'
        }));
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Verification failed';
      setTwoFAState(prev => ({
        ...prev,
        isVerifying: false,
        error: errorMessage
      }));
    }
  };

  // Calculate total balance in USD
  const calculateTotalBalanceUSD = (wallets: EnhancedWallet[]): number => {
    return wallets.reduce((total, wallet) => total + wallet.totalBalanceUSD, 0);
  };

  // Calculate total number of active chains
  const calculateActiveChains = (wallets: EnhancedWallet[]): number => {
    const uniqueChains = new Set<number>();

    wallets.forEach(wallet => {
      if (wallet.portfolioData) {
        wallet.portfolioData.forEach(chain => {
          uniqueChains.add(chain.chainId);
        });
      }
    });

    return uniqueChains.size;
  };

  // Memoize the total balance calculation
  const totalBalanceUSD = calculateTotalBalanceUSD(wallets);
  const activeChains = calculateActiveChains(wallets);



  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchWallets();
    setRefreshing(false);
  };

  // --- UI ---
  return (
    <div className="min-h-[calc(100vh-80px)] bg-gray-50 p-4 md:p-8">
      {/* Header */}
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 flex items-center gap-3">
              <FiWallet className="text-blue-600" />
              Wallet Management
            </h1>
            <p className="mt-2 text-gray-600">
              Manage your cryptocurrency wallets, view portfolio, and track assets across multiple chains
            </p>
          </div>

          <div className="mt-4 md:mt-0 flex items-center space-x-3">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <FiRefreshCw className={`mr-2 ${refreshing ? 'animate-spin' : ''}`} size={16} />
              <span>Refresh</span>
            </button>
            <button
              onClick={() => setActiveModal('import')}
              className="flex items-center px-4 py-2.5 bg-white text-gray-900 rounded-lg transition-colors border border-gray-300 hover:bg-gray-50"
            >
              <FiUpload className="mr-2" size={16} />
              <span>Import</span>
            </button>
            <button
              onClick={() => setActiveModal('create')}
              className="flex items-center px-4 py-2.5 bg-blue-600 text-white rounded-lg transition-colors hover:bg-blue-700"
            >
              <FiPlus className="mr-2" size={16} />
              <span>New Wallet</span>
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Balance (USD)</p>
                <p className="text-2xl font-bold mt-1 text-gray-900">
                  {formatCurrency(totalBalanceUSD)}
                </p>
              </div>
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <FiTrendingUp className="w-6 h-6" />
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Wallets</p>
                <p className="text-2xl font-bold mt-1 text-gray-900">
                  {wallets.length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <FiWallet className="w-6 h-6" />
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Chains</p>
                <p className="text-2xl font-bold mt-1 text-gray-900">
                  {activeChains}
                </p>
              </div>
              <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                <FiSettings className="w-6 h-6" />
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="mb-6">
          <div className="relative max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiSearch className="h-5 w-5 text-gray-700" />
            </div>
            <input
              type="text"
              placeholder="Search wallets by name or address..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-3 py-2.5 bg-white border border-gray-200 rounded-lg text-black placeholder-gray-700 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
            />
          </div>
        </div>

        {/* Wallets List */}
        <div className="space-y-4 z-10">
          {loading ? (
            // Loading skeleton
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-24 bg-gray-100 rounded-xl animate-pulse"></div>
              ))}
            </div>
          ) : filteredWallets.length > 0 ? (
            // Modern Wallet Cards
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="show"
              className="grid grid-cols-1 lg:grid-cols-2 gap-6"
            >
              <AnimatePresence>
                {filteredWallets.map((wallet) => (
                  <motion.div key={wallet.id} variants={itemVariants}>
                    <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm hover:shadow-md transition-all duration-200">
                      {/* Wallet Header */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <FiWallet className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">{wallet.name}</h3>
                            <p className="text-sm text-gray-500">
                              {wallet.address.slice(0, 6)}...{wallet.address.slice(-4)}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {wallet.isPrimary && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              <FiStar className="w-3 h-3 mr-1" />
                              Primary
                            </span>
                          )}
                          <div className="relative">
                            <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                              <FiMoreVertical className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>

                      {/* Balance Display */}
                      <div className="mb-4">
                        <div className="text-2xl font-bold text-gray-900 mb-1">
                          {formatCurrency(wallet.totalBalanceUSD)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {wallet.portfolioData?.length || 0} chains • {wallet.portfolioData?.reduce((acc, chain) => acc + chain.tokens.length, 0) || 0} tokens
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="grid grid-cols-4 gap-2">
                        <button
                          onClick={() => handleDeposit(wallet)}
                          className="flex flex-col items-center p-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        >
                          <FiArrowDownLeft className="w-4 h-4 mb-1" />
                          <span className="text-xs">Deposit</span>
                        </button>
                        <button
                          onClick={() => handleWithdraw(wallet)}
                          className="flex flex-col items-center p-3 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                        >
                          <FiArrowUpRight className="w-4 h-4 mb-1" />
                          <span className="text-xs">Send</span>
                        </button>
                        <button
                          onClick={() => handleCopyAddress(wallet.address)}
                          className="flex flex-col items-center p-3 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors"
                        >
                          {copiedAddress === wallet.address ? (
                            <FiCheck className="w-4 h-4 mb-1" />
                          ) : (
                            <FiCopy className="w-4 h-4 mb-1" />
                          )}
                          <span className="text-xs">Copy</span>
                        </button>
                        <button
                          onClick={() => handleExportWallet(wallet)}
                          className="flex flex-col items-center p-3 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors"
                        >
                          <FiDownload className="w-4 h-4 mb-1" />
                          <span className="text-xs">Export</span>
                        </button>
                      </div>

                      {/* Portfolio Preview */}
                      {wallet.portfolioData && wallet.portfolioData.length > 0 && (
                        <div className="mt-4 pt-4 border-t border-gray-100">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">Top Holdings</span>
                            <button
                              onClick={() => {
                                setSelectedWallet(wallet);
                                setActiveModal('portfolio');
                              }}
                              className="text-blue-600 hover:text-blue-700 font-medium"
                            >
                              View All
                            </button>
                          </div>
                          <div className="mt-2 space-y-1">
                            {wallet.portfolioData.slice(0, 2).map((chain, idx) => (
                              <div key={idx} className="flex items-center justify-between text-sm">
                                <span className="text-gray-600">{chain.chainName}</span>
                                <span className="font-medium text-gray-900">
                                  {chain.tokens.length} tokens
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </motion.div>
          ) : (
            // Empty state
            <div className="text-center py-16 bg-white rounded-xl border-2 border-dashed border-gray-300">
              <FiWallet className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">No wallets found</h3>
              <p className="mt-2 text-sm text-gray-500">
                {searchQuery ? 'Try a different search term' : 'Get started by creating your first wallet'}
              </p>
              <div className="mt-6">
                <button
                  onClick={handleCreateWallet}
                  disabled={isSubmitting}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <FiPlus className="-ml-1 mr-2 h-5 w-5" />
                  {isSubmitting ? 'Creating...' : 'Create Wallet'}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Import Wallet Modal */}
      {activeModal === 'import' && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Import Wallet</h3>
                <button
                  onClick={() => setActiveModal(null)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <FiX size={24} />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label htmlFor="wallet-name" className="block text-sm font-medium text-gray-700 mb-2">
                    Wallet Name (Optional)
                  </label>
                  <input
                    type="text"
                    id="wallet-name"
                    value={walletName}
                    onChange={(e) => setWalletName(e.target.value)}
                    placeholder="e.g. My Trading Wallet"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label htmlFor="private-key" className="block text-sm font-medium text-gray-700 mb-2">
                    Private Key
                  </label>
                  <textarea
                    id="private-key"
                    rows={3}
                    value={privateKey}
                    onChange={(e) => setPrivateKey(e.target.value)}
                    placeholder="Enter your private key (64 characters)..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-900 font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <p className="mt-2 text-xs text-gray-500 flex items-center">
                    <FiShield className="w-3 h-3 mr-1" />
                    Your private key is encrypted and stored securely
                  </p>
                </div>

                <div className="pt-4">
                  <button
                    onClick={handleImportWallet}
                    disabled={!privateKey || isSubmitting}
                    className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center">
                        <FiRefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
                        Importing...
                      </div>
                    ) : (
                      'Import Wallet'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Create Wallet Modal */}
      {activeModal === 'create' && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Create New Wallet</h3>
                <button
                  onClick={() => setActiveModal(null)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <FiX size={24} />
                </button>
              </div>

              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <FiShield className="w-5 h-5 text-blue-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="text-sm font-medium text-blue-900 mb-1">Secure Wallet Generation</h4>
                      <p className="text-sm text-blue-700">
                        A new wallet will be generated with a unique private key and mnemonic phrase.
                        Make sure to save your recovery information securely.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="pt-2">
                  <button
                    onClick={handleCreateWallet}
                    disabled={isSubmitting}
                    className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center">
                        <FiRefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
                        Creating...
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <FiPlus className="-ml-1 mr-2 h-4 w-4" />
                        Create Wallet
                      </div>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* 2FA Verification Modal */}
      {activeModal === '2fa' && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Two-Factor Authentication</h3>
                <button
                  onClick={() => {
                    setActiveModal(null);
                    setTwoFAState({
                      isRequired: false,
                      isVerifying: false,
                      code: '',
                      error: null,
                      pendingAction: null
                    });
                  }}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <FiX size={24} />
                </button>
              </div>

              <div className="space-y-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <FiShield className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="text-sm font-medium text-yellow-900 mb-1">Security Verification Required</h4>
                      <p className="text-sm text-yellow-700">
                        Please enter the 6-digit verification code sent to your Telegram account.
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <label htmlFor="2fa-code" className="block text-sm font-medium text-gray-700 mb-2">
                    Verification Code
                  </label>
                  <input
                    type="text"
                    id="2fa-code"
                    value={twoFAState.code}
                    onChange={(e) => setTwoFAState(prev => ({ ...prev, code: e.target.value }))}
                    placeholder="Enter 6-digit code"
                    maxLength={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-900 text-center text-lg font-mono focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {twoFAState.error && (
                    <p className="mt-2 text-sm text-red-600">{twoFAState.error}</p>
                  )}
                </div>

                <div className="pt-2">
                  <button
                    onClick={handle2FAVerification}
                    disabled={!twoFAState.code || twoFAState.code.length !== 6 || twoFAState.isVerifying}
                    className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {twoFAState.isVerifying ? (
                      <div className="flex items-center">
                        <FiRefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
                        Verifying...
                      </div>
                    ) : (
                      'Verify Code'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Export Wallet Modal */}
      {activeModal === 'export' && exportedData && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Export Private Key</h3>
                <button
                  onClick={() => {
                    setActiveModal(null);
                    setExportedData(null);
                    setShowPrivateKey(false);
                  }}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <FiX size={24} />
                </button>
              </div>

              <div className="space-y-4">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <FiShield className="w-5 h-5 text-red-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="text-sm font-medium text-red-900 mb-1">Security Warning</h4>
                      <p className="text-sm text-red-700">
                        Never share your private key with anyone. Anyone with access to this key can control your wallet.
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Private Key</label>
                  <div className="relative">
                    <textarea
                      readOnly
                      value={showPrivateKey ? exportedData.privateKey : '•'.repeat(64)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-900 font-mono text-sm focus:outline-none resize-none"
                      rows={3}
                    />
                    <button
                      onClick={() => setShowPrivateKey(!showPrivateKey)}
                      className="absolute top-2 right-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPrivateKey ? <FiEyeOff size={16} /> : <FiEye size={16} />}
                    </button>
                  </div>
                </div>

                {exportedData.phrase && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Recovery Phrase</label>
                    <div className="relative">
                      <textarea
                        readOnly
                        value={showPrivateKey ? exportedData.phrase : '•'.repeat(exportedData.phrase.length)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-900 text-sm focus:outline-none resize-none"
                        rows={2}
                      />
                    </div>
                  </div>
                )}

                <div className="flex space-x-3 pt-2">
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(exportedData.privateKey);
                      setSuccess('Private key copied to clipboard');
                      setTimeout(() => setSuccess(null), 3000);
                    }}
                    className="flex-1 flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <FiCopy className="mr-2 h-4 w-4" />
                    Copy
                  </button>
                  <button
                    onClick={() => {
                      setActiveModal(null);
                      setExportedData(null);
                      setShowPrivateKey(false);
                    }}
                    className="flex-1 flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Success Toast */}
      {success && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          className="fixed bottom-4 right-4 bg-green-600 text-white px-4 py-3 rounded-lg shadow-lg z-50 flex items-center"
        >
          <FiCheck className="mr-2" />
          {success}
          <button className="ml-3" onClick={() => setSuccess(null)}>
            <FiX />
          </button>
        </motion.div>
      )}

      {/* Error Toast */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          className="fixed bottom-4 right-4 bg-red-600 text-white px-4 py-3 rounded-lg shadow-lg z-50 flex items-center"
        >
          <FiX className="mr-2" />
          {error}
          <button className="ml-3" onClick={() => setError(null)}>
            <FiX />
          </button>
        </motion.div>
      )}
    </div>
  );
}
