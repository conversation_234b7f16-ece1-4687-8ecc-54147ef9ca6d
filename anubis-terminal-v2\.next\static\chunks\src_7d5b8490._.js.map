{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/TimeFilter/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { FaCaretDown } from \"react-icons/fa\";\r\n\r\nexport default function TimeFilter({ timeOptions, selectedTime, handleTimeSelect }: {\r\n    timeOptions: string[],\r\n    selectedTime: string,\r\n    handleTimeSelect: (time: string) => void\r\n}) {\r\n\r\n\r\n    return (\r\n        <div className=\"rounded-sm border border-white/10 flex\">\r\n            {timeOptions.map((time, index) => (\r\n                <button\r\n                    key={index}\r\n                    onClick={() => handleTimeSelect(time)}\r\n                    className={`px-2.5 py-1 rounded-sm hover:bg-white/10 transition-colors text-xs hover:cursor-pointer ${time === selectedTime ? \"bg-white/10\" : \"\"\r\n                        }`}\r\n                >\r\n                    {time.toUpperCase()}\r\n                </button>\r\n            ))}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport function TimeFilterDropdown({ timeOptions, selectedTime, handleTimeSelect }: {\r\n    timeOptions: string[],\r\n    selectedTime: string,\r\n    handleTimeSelect: (time: string) => void\r\n}) {\r\n    const [isOpen, setIsOpen] = useState(false);\r\n    return (\r\n        <div className=\"relative flex items-center gap-x-1\">\r\n            <button\r\n                onClick={() => setIsOpen(!isOpen)}\r\n                className=\"px-2.5 py-1 rounded-sm border border-white/10 text-xs hover:bg-white/10 transition-colors flex items-center gap-x-1\"\r\n            >\r\n                {selectedTime.toUpperCase()}\r\n                <FaCaretDown className=\"text-white\" />\r\n            </button>\r\n            {isOpen && (\r\n                <div className=\"absolute top-8 left-0 mt-1 w-20 rounded-sm border border-white/10 bg-black z-10 flex flex-col gap-y-1\">\r\n                    {timeOptions.map((time, index) => (\r\n                        <motion.button\r\n                            key={index}\r\n                            onClick={() => {\r\n                                handleTimeSelect(time);\r\n                                setIsOpen(false);\r\n                            }}\r\n                            className={`px-2.5 py-1 hover:bg-white/10 transition-colors text-xs hover:cursor-pointer ${time === selectedTime ? \"bg-white/10\" : \"\"}`}\r\n                        >{time.toUpperCase()}\r\n                        </motion.button>\r\n                    ))}\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;AAHA;;;;AAKe,SAAS,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAI/E;IAGG,qBACI,6LAAC;QAAI,WAAU;kBACV,YAAY,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;gBAEG,SAAS,IAAM,iBAAiB;gBAChC,WAAW,CAAC,wFAAwF,EAAE,SAAS,eAAe,gBAAgB,IACxI;0BAEL,KAAK,WAAW;eALZ;;;;;;;;;;AAUzB;KArBwB;AAuBjB,SAAS,mBAAmB,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAI/E;;IACG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBACG,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;oBAET,aAAa,WAAW;kCACzB,6LAAC,iJAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;YAE1B,wBACG,6LAAC;gBAAI,WAAU;0BACV,YAAY,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBAEV,SAAS;4BACL,iBAAiB;4BACjB,UAAU;wBACd;wBACA,WAAW,CAAC,6EAA6E,EAAE,SAAS,eAAe,gBAAgB,IAAI;kCACzI,KAAK,WAAW;uBANT;;;;;;;;;;;;;;;;AAajC;GAhCgB;MAAA", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/TimeFilter/StatsFilter/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { IoFilter } from \"react-icons/io5\";\r\n\r\n\r\nexport default function StatsFilter() {\r\n\r\n    return (\r\n        <motion.button className=\"flex items-center gap-x-2 border border-white/30 px-2.5 py-1 rounded-sm  text-xs\"\r\n            whileTap={{\r\n                scale: 1.03\r\n            }}\r\n            whileHover={{\r\n                cursor: \"pointer\"\r\n            }}\r\n        >\r\n            <IoFilter className=\"text-white\" /> <span>Filter</span>\r\n        </motion.button>\r\n    )\r\n}\r\n\r\nfunction FilterDropdown() {\r\n    return (\r\n        <>\r\n\r\n\r\n        </>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAMe,SAAS;IAEpB,qBACI,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QAAC,WAAU;QACrB,UAAU;YACN,OAAO;QACX;QACA,YAAY;YACR,QAAQ;QACZ;;0BAEA,6LAAC,kJAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAe;0BAAC,6LAAC;0BAAK;;;;;;;;;;;;AAGtD;KAdwB;AAgBxB,SAAS;IACL,qBACI;AAKR;MAPS", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/hooks/useGetMarketData.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from 'react';\r\nimport { getTrendingTokens, getNewTokenPairs, getGainerTokenPairs } from '@/services/api/flooz';\r\n\r\ninterface UseGetMarketDataOptions {\r\n    networks: string | undefined;\r\n    time: string;\r\n    pollingInterval?: number; // in ms\r\n}\r\n\r\ninterface UseGetMarketDataResult<TokensType = any, PairsType = any> {\r\n    trendingTokens: TokensType | null;\r\n    newPairs: PairsType | null;\r\n    gainers: PairsType | null;\r\n    loading: boolean;\r\n    isRefetching: boolean;\r\n    error: Error | null;\r\n    refetch: () => void;\r\n}\r\n\r\nexport function useGetMarketData({ networks, time, pollingInterval }: UseGetMarketDataOptions): UseGetMarketDataResult {\r\n    const [trendingTokens, setTrendingTokens] = useState<any | null>(null);\r\n    const [newPairs, setNewPairs] = useState<any | null>(null);\r\n    const [gainers, setGainers] = useState<any | null>(null);\r\n    const [loading, setLoading] = useState<boolean>(false);\r\n    const [isRefetching, setIsRefetching] = useState<boolean>(false);\r\n    const [error, setError] = useState<Error | null>(null);\r\n    const pollingRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n    const fetchData = useCallback(async (isPolling = false) => {\r\n        if (!isPolling) {\r\n            setLoading(true);\r\n        } else {\r\n            setIsRefetching(true);\r\n        }\r\n        setError(null);\r\n        try {\r\n            const [tokens, pairs, gainers] = await Promise.all([\r\n                getTrendingTokens(networks as string, time),\r\n                getNewTokenPairs(networks as string, time),\r\n                getGainerTokenPairs(networks as string, time)\r\n            ]);\r\n            setTrendingTokens(tokens);\r\n            setNewPairs(pairs);\r\n            setGainers(gainers);\r\n        } catch (err) {\r\n            setError(err as Error);\r\n        } finally {\r\n            if (!isPolling) {\r\n                setLoading(false);\r\n            } else {\r\n                setIsRefetching(false);\r\n            }\r\n        }\r\n    }, [networks, time]);\r\n\r\n    // Initial fetch and refetch on param change\r\n    useEffect(() => {\r\n        fetchData();\r\n        // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [fetchData]);\r\n\r\n    // Polling logic\r\n    useEffect(() => {\r\n        if (pollingInterval && pollingInterval > 0) {\r\n            pollingRef.current = setInterval(() => fetchData(true), pollingInterval);\r\n            return () => {\r\n                if (pollingRef.current) clearInterval(pollingRef.current);\r\n            };\r\n        }\r\n        return undefined;\r\n    }, [fetchData, pollingInterval]);\r\n\r\n    const refetch = useCallback(() => {\r\n        fetchData(true);\r\n    }, [fetchData]);\r\n\r\n    return {\r\n        trendingTokens,\r\n        newPairs,\r\n        gainers,\r\n        loading,\r\n        isRefetching,\r\n        error,\r\n        refetch,\r\n    };\r\n} "], "names": [], "mappings": ";;;AAAA;AACA;;;;AAkBO,SAAS,iBAAiB,EAAE,QAAQ,EAAE,IAAI,EAAE,eAAe,EAA2B;;IACzF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEjD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO,YAAY,KAAK;YAClD,IAAI,CAAC,WAAW;gBACZ,WAAW;YACf,OAAO;gBACH,gBAAgB;YACpB;YACA,SAAS;YACT,IAAI;gBACA,MAAM,CAAC,QAAQ,OAAO,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAC/C,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD,EAAE,UAAoB;oBACtC,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD,EAAE,UAAoB;oBACrC,CAAA,GAAA,kIAAA,CAAA,sBAAmB,AAAD,EAAE,UAAoB;iBAC3C;gBACD,kBAAkB;gBAClB,YAAY;gBACZ,WAAW;YACf,EAAE,OAAO,KAAK;gBACV,SAAS;YACb,SAAU;gBACN,IAAI,CAAC,WAAW;oBACZ,WAAW;gBACf,OAAO;oBACH,gBAAgB;gBACpB;YACJ;QACJ;kDAAG;QAAC;QAAU;KAAK;IAEnB,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN;QACA,uDAAuD;QAC3D;qCAAG;QAAC;KAAU;IAEd,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN,IAAI,mBAAmB,kBAAkB,GAAG;gBACxC,WAAW,OAAO,GAAG;kDAAY,IAAM,UAAU;iDAAO;gBACxD;kDAAO;wBACH,IAAI,WAAW,OAAO,EAAE,cAAc,WAAW,OAAO;oBAC5D;;YACJ;YACA,OAAO;QACX;qCAAG;QAAC;QAAW;KAAgB;IAE/B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YACxB,UAAU;QACd;gDAAG;QAAC;KAAU;IAEd,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;AACJ;GAlEgB", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Trade/Trending/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState } from \"react\";\r\nimport { useWeb3 } from \"@/contexts/Web3Context\";\r\nimport { TimeFilterDropdown } from \"@/components/Terminal/TimeFilter\";\r\nimport StatsFilter from \"@/components/Terminal/TimeFilter/StatsFilter\";\r\nimport { useGetMarketData } from \"@/hooks/useGetMarketData\";\r\nimport { getTimeAgo, getVolumeByTimeOption, getPriceChangeByTimeOption } from \"@/utils/data\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nconst timeOptions = [\"1h\", \"4h\", \"12h\", \"24h\"];\r\ntype TimeOption = string;\r\n\r\ninterface TrendingToken {\r\n    id: string;\r\n    details: {\r\n        name: string;\r\n        symbol: string;\r\n        address: string;\r\n        imageThumbUrl?: string;\r\n    };\r\n    activity: {\r\n        createdAt: number;\r\n        change1?: string;\r\n        change4?: string;\r\n        change12?: string;\r\n        change24?: string;\r\n        volume1?: string;\r\n        volume4?: string;\r\n        volume12?: string;\r\n        volume24?: string;\r\n        [key: string]: any;\r\n    };\r\n    marketData: {\r\n        marketCap: number;\r\n        [key: string]: any;\r\n    };\r\n}\r\n\r\nexport default function Trending() {\r\n    const { selectedChain } = useWeb3();\r\n    const { push } = useRouter();\r\n    const [selectedTime, setSelectedTime] = useState<TimeOption>(\"1h\");\r\n    const { trendingTokens, loading, error, refetch } = useGetMarketData({ networks: selectedChain?.slug, time: selectedTime, pollingInterval: 10000 });\r\n\r\n    const handleTimeSelect = (time: TimeOption) => setSelectedTime(time);\r\n\r\n    return (\r\n        <div className=\"w-full p-3 flex flex-col gap-2 overflow-hidden row-span-1 border-b border-white/20\">\r\n            <div className=\"flex items-center justify-start gap-x-2 mb-2\">\r\n                <h5 className=\"font-space-grotesk text-xs font-bold\">Trending</h5>\r\n                <TimeFilterDropdown timeOptions={timeOptions} selectedTime={selectedTime} handleTimeSelect={handleTimeSelect} />\r\n                <StatsFilter />\r\n            </div>\r\n            <div className=\"overflow-x-scroll scrollbar-hidden\">\r\n                <table className=\"w-full min-w-[420px] text-left text-xs border-separate border-spacing-y-1\">\r\n                    <thead>\r\n                        <tr className=\"text-white/60\">\r\n                            <th className=\"px-2 py-1 font-semibold\">Asset</th>\r\n                            <th className=\"px-2 py-1 font-semibold\">Age</th>\r\n                            <th className=\"px-2 py-1 font-semibold\">{selectedTime.toUpperCase()}</th>\r\n                            <th className=\"px-2 py-1 font-semibold\">Volume</th>\r\n                            <th className=\"px-2 py-1 font-semibold\">MCap</th>\r\n                        </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                        {loading && (\r\n                            <tr><td colSpan={5} className=\"text-center py-4 text-white/50\">Loading...</td></tr>\r\n                        )}\r\n                        {error && (\r\n                            <tr><td colSpan={5} className=\"text-center py-4 text-red-400\">Error loading data</td></tr>\r\n                        )}\r\n                        {trendingTokens && (trendingTokens as TrendingToken[]).slice(0, 8).map((token) => {\r\n                            // Fallbacks for missing activity fields\r\n                            const activity = {\r\n                                change1: \"0\", change4: \"0\", change12: \"0\", change24: \"0\",\r\n                                volume1: \"0\", volume4: \"0\", volume12: \"0\", volume24: \"0\",\r\n                                ...token.activity\r\n                            };\r\n                            const priceChange = getPriceChangeByTimeOption(activity, selectedTime);\r\n                            const volume = getVolumeByTimeOption(activity, selectedTime);\r\n                            return (\r\n                                <tr key={token.id} className=\"hover:bg-white/5 rounded transition cursor-pointer\" onClick={() => selectedChain?.slug && push(`/terminal/trade/${selectedChain.slug}/${token.details.address}`)}>\r\n                                    {/* Asset */}\r\n                                    <td className=\"px-2 py-1\">\r\n                                        <div className=\"flex items-center gap-2\">\r\n                                            {token.details.imageThumbUrl ? (\r\n                                                <div className=\"w-5 h-5 relative\">\r\n                                                    <img src={token.details.imageThumbUrl} alt={token.details.name} className=\"w-5 h-5 rounded-full\" />\r\n                                                    <img src={selectedChain?.logo} alt=\"\" className=\"w-2.5 h-2.5 absolute -bottom-1 -right-1\" />\r\n                                                </div>\r\n                                            ) : (\r\n                                                <div className=\"w-5 h-5 bg-gray-700 rounded-full flex items-center justify-center text-[10px] relative\">\r\n                                                    {token.details.name.charAt(0)}\r\n                                                </div>\r\n                                            )}\r\n                                            <span className=\"font-medium text-white truncate max-w-[70px]\">{token.details.name}</span>\r\n                                            <span className=\"text-white/40 text-[10px]\">{token.details.symbol}</span>\r\n                                        </div>\r\n                                    </td>\r\n                                    {/* Age */}\r\n                                    <td className=\"px-2 py-1 text-white/70\">{getTimeAgo(token.activity.createdAt)}</td>\r\n                                    {/* The Price Timing */}\r\n                                    <td className=\"px-2 py-1\">\r\n                                        <span className={priceChange > 0 ? \"text-green-500\" : priceChange < 0 ? \"text-red-500\" : \"text-white/70\"}>\r\n                                            {priceChange > 0 ? \"+\" : \"\"}\r\n                                            {priceChange.toFixed(1)}%\r\n                                        </span>\r\n                                    </td>\r\n                                    {/* Volume */}\r\n                                    <td className=\"px-2 py-1 font-mono text-white/80\">${volume.toLocaleString()}</td>\r\n                                    {/* Market Cap */}\r\n                                    <td className=\"px-2 py-1 text-white/70 font-mono\">${Number(token.marketData.marketCap).toLocaleString()}</td>\r\n                                </tr>\r\n                            );\r\n                        })}\r\n                        {trendingTokens && (trendingTokens as TrendingToken[]).length === 0 && !loading && (\r\n                            <tr><td colSpan={5} className=\"text-center py-4 text-white/50\">No trending tokens</td></tr>\r\n                        )}\r\n                    </tbody>\r\n                </table>\r\n            </div>\r\n        </div>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;;AASA,MAAM,cAAc;IAAC;IAAM;IAAM;IAAO;CAAM;AA6B/B,SAAS;;IACpB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAC7D,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE;QAAE,UAAU,eAAe;QAAM,MAAM;QAAc,iBAAiB;IAAM;IAEjJ,MAAM,mBAAmB,CAAC,OAAqB,gBAAgB;IAE/D,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,6LAAC,wJAAA,CAAA,qBAAkB;wBAAC,aAAa;wBAAa,cAAc;wBAAc,kBAAkB;;;;;;kCAC5F,6LAAC,uKAAA,CAAA,UAAW;;;;;;;;;;;0BAEhB,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAM,WAAU;;sCACb,6LAAC;sCACG,cAAA,6LAAC;gCAAG,WAAU;;kDACV,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAG,WAAU;kDAA2B,aAAa,WAAW;;;;;;kDACjE,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;;;;;;;;;;;;sCAGhD,6LAAC;;gCACI,yBACG,6LAAC;8CAAG,cAAA,6LAAC;wCAAG,SAAS;wCAAG,WAAU;kDAAiC;;;;;;;;;;;gCAElE,uBACG,6LAAC;8CAAG,cAAA,6LAAC;wCAAG,SAAS;wCAAG,WAAU;kDAAgC;;;;;;;;;;;gCAEjE,kBAAkB,AAAC,eAAmC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;oCACpE,wCAAwC;oCACxC,MAAM,WAAW;wCACb,SAAS;wCAAK,SAAS;wCAAK,UAAU;wCAAK,UAAU;wCACrD,SAAS;wCAAK,SAAS;wCAAK,UAAU;wCAAK,UAAU;wCACrD,GAAG,MAAM,QAAQ;oCACrB;oCACA,MAAM,cAAc,CAAA,GAAA,uHAAA,CAAA,6BAA0B,AAAD,EAAE,UAAU;oCACzD,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,wBAAqB,AAAD,EAAE,UAAU;oCAC/C,qBACI,6LAAC;wCAAkB,WAAU;wCAAqD,SAAS,IAAM,eAAe,QAAQ,KAAK,CAAC,gBAAgB,EAAE,cAAc,IAAI,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE;;0DAEzL,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAI,WAAU;;wDACV,MAAM,OAAO,CAAC,aAAa,iBACxB,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,KAAK,MAAM,OAAO,CAAC,aAAa;oEAAE,KAAK,MAAM,OAAO,CAAC,IAAI;oEAAE,WAAU;;;;;;8EAC1E,6LAAC;oEAAI,KAAK,eAAe;oEAAM,KAAI;oEAAG,WAAU;;;;;;;;;;;iFAGpD,6LAAC;4DAAI,WAAU;sEACV,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;sEAGnC,6LAAC;4DAAK,WAAU;sEAAgD,MAAM,OAAO,CAAC,IAAI;;;;;;sEAClF,6LAAC;4DAAK,WAAU;sEAA6B,MAAM,OAAO,CAAC,MAAM;;;;;;;;;;;;;;;;;0DAIzE,6LAAC;gDAAG,WAAU;0DAA2B,CAAA,GAAA,uHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,QAAQ,CAAC,SAAS;;;;;;0DAE5E,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAK,WAAW,cAAc,IAAI,mBAAmB,cAAc,IAAI,iBAAiB;;wDACpF,cAAc,IAAI,MAAM;wDACxB,YAAY,OAAO,CAAC;wDAAG;;;;;;;;;;;;0DAIhC,6LAAC;gDAAG,WAAU;;oDAAoC;oDAAE,OAAO,cAAc;;;;;;;0DAEzE,6LAAC;gDAAG,WAAU;;oDAAoC;oDAAE,OAAO,MAAM,UAAU,CAAC,SAAS,EAAE,cAAc;;;;;;;;uCA9BhG,MAAM,EAAE;;;;;gCAiCzB;gCACC,kBAAkB,AAAC,eAAmC,MAAM,KAAK,KAAK,CAAC,yBACpE,6LAAC;8CAAG,cAAA,6LAAC;wCAAG,SAAS;wCAAG,WAAU;kDAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3F;GArFwB;;QACM,kIAAA,CAAA,UAAO;QAChB,qIAAA,CAAA,YAAS;QAE0B,mIAAA,CAAA,mBAAgB;;;KAJhD", "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Trade/Trending/RecentlyViewed/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useWeb3 } from \"@/contexts/Web3Context\";\r\nimport { getTimeAgo } from \"@/utils/data\";\r\nimport React from \"react\";\r\n\r\nexport default function RecentlyViewed() {\r\n    const { recentlyViewed, selectedChain } = useWeb3();\r\n    const filtered = recentlyViewed.filter(token => token.chain === selectedChain?.slug);\r\n    return (\r\n        <div className=\"row-span-1 w-full p-3 min-h-[30%] overflow-auto scrollbar-hidden border-b border-white/20\">\r\n            <h5 className=\"font-space-grotesk text-xs font-bold\">Recently Viewed</h5>\r\n            <div className=\"overflow-x-scroll scrollbar-hidden\">\r\n                <table className=\"w-full min-w-[420px] text-left text-xs border-separate border-spacing-y-1\">\r\n                    <thead>\r\n                        <tr className=\"text-white/60\">\r\n                            <th className=\"px-2 py-1 font-semibold\">Asset</th>\r\n                            <th className=\"px-2 py-1 font-semibold\">Price</th>\r\n                            <th className=\"px-2 py-1 font-semibold\">Market Cap</th>\r\n                        </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                        {filtered.map((token, index) => {\r\n                            const tokenData = token.data?.results?.[0];\r\n                            if (!tokenData) return null;\r\n                            const { details, marketData, activity } = tokenData;\r\n                            return (\r\n                                <tr key={index} className=\"hover:bg-white/5 rounded transition cursor-pointer\">\r\n                                    <td className=\"px-2 py-1\">\r\n                                        <div className=\"flex items-center gap-2\">\r\n                                            {details?.imageThumbUrl ? (\r\n                                                <div className=\"w-5 h-5 relative\">\r\n                                                    <img src={details.imageThumbUrl} alt={details.name} className=\"w-5 h-5 rounded-full\" />\r\n                                                    <img src={selectedChain?.logo} alt=\"\" className=\"w-2.5 h-2.5 absolute -bottom-1 -right-1\" />\r\n                                                </div>\r\n                                            ) : (\r\n                                                <div className=\"w-5 h-5 bg-gray-700 rounded-full flex items-center justify-center text-[10px] relative\">\r\n                                                    {details?.name?.charAt(0) ?? '?'}\r\n                                                    <img src={selectedChain?.logo} alt=\"\" className=\"w-2.5 h-2.5 absolute -bottom-1 -right-1\" />\r\n                                                </div>\r\n                                            )}\r\n                                            <div className=\"flex flex-col\">\r\n                                                <span className=\"font-medium text-white truncate max-w-[70px]\">{details?.name ?? 'Unknown'}</span>\r\n                                                <span className=\"text-white/40 text-[10px]\">{details?.symbol ?? ''}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-2 py-1 text-white/70\">{marketData?.priceUSD ? `$${parseFloat(marketData.priceUSD).toLocaleString(undefined, { maximumFractionDigits: 6 })}` : '-'}</td>\r\n                                    <td className=\"px-2 py-1 text-white/70\">{marketData?.marketCap ? `$${parseFloat(marketData.marketCap).toLocaleString()}` : '-'}</td>\r\n                                </tr>\r\n                            );\r\n                        })}\r\n                    </tbody>\r\n                </table>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;;AADA;;AAKe,SAAS;;IACpB,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChD,MAAM,WAAW,eAAe,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK,KAAK,eAAe;IAC/E,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAG,WAAU;0BAAuC;;;;;;0BACrD,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAM,WAAU;;sCACb,6LAAC;sCACG,cAAA,6LAAC;gCAAG,WAAU;;kDACV,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;;;;;;;;;;;;sCAGhD,6LAAC;sCACI,SAAS,GAAG,CAAC,CAAC,OAAO;gCAClB,MAAM,YAAY,MAAM,IAAI,EAAE,SAAS,CAAC,EAAE;gCAC1C,IAAI,CAAC,WAAW,OAAO;gCACvB,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;gCAC1C,qBACI,6LAAC;oCAAe,WAAU;;sDACtB,6LAAC;4CAAG,WAAU;sDACV,cAAA,6LAAC;gDAAI,WAAU;;oDACV,SAAS,8BACN,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAI,KAAK,QAAQ,aAAa;gEAAE,KAAK,QAAQ,IAAI;gEAAE,WAAU;;;;;;0EAC9D,6LAAC;gEAAI,KAAK,eAAe;gEAAM,KAAI;gEAAG,WAAU;;;;;;;;;;;6EAGpD,6LAAC;wDAAI,WAAU;;4DACV,SAAS,MAAM,OAAO,MAAM;0EAC7B,6LAAC;gEAAI,KAAK,eAAe;gEAAM,KAAI;gEAAG,WAAU;;;;;;;;;;;;kEAGxD,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAK,WAAU;0EAAgD,SAAS,QAAQ;;;;;;0EACjF,6LAAC;gEAAK,WAAU;0EAA6B,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;sDAI5E,6LAAC;4CAAG,WAAU;sDAA2B,YAAY,WAAW,CAAC,CAAC,EAAE,WAAW,WAAW,QAAQ,EAAE,cAAc,CAAC,WAAW;gDAAE,uBAAuB;4CAAE,IAAI,GAAG;;;;;;sDAChK,6LAAC;4CAAG,WAAU;sDAA2B,YAAY,YAAY,CAAC,CAAC,EAAE,WAAW,WAAW,SAAS,EAAE,cAAc,IAAI,GAAG;;;;;;;mCArBtH;;;;;4BAwBjB;;;;;;;;;;;;;;;;;;;;;;;AAMxB;GAnDwB;;QACsB,kIAAA,CAAA,UAAO;;;KAD7B", "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/hooks/useTrading.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport {\n  getOpenOrders,\n  getOrderHistory,\n  getPositions,\n  getMarketData,\n  type Order,\n  type Position\n} from '@/services/bot/trading';\n\ninterface UseTradingOptions {\n  tokenAddress?: string;\n  chainId?: number;\n  pollingInterval?: number;\n}\n\ninterface UseTradingResult {\n  // Orders\n  openOrders: Order[];\n  orderHistory: Order[];\n  \n  // Positions\n  positions: Position[];\n  \n  // Market Data\n  marketData: {\n    price: number;\n    priceChange24h: number;\n    volume24h: number;\n    marketCap: number;\n    liquidity: number;\n  } | null;\n  \n  // Loading states\n  ordersLoading: boolean;\n  positionsLoading: boolean;\n  marketDataLoading: boolean;\n  \n  // Error states\n  ordersError: string | null;\n  positionsError: string | null;\n  marketDataError: string | null;\n  \n  // Actions\n  refreshOrders: () => Promise<void>;\n  refreshPositions: () => Promise<void>;\n  refreshMarketData: () => Promise<void>;\n  refreshAll: () => Promise<void>;\n}\n\nexport const useTrading = ({\n  tokenAddress,\n  chainId = 1,\n  pollingInterval = 30000\n}: UseTradingOptions = {}): UseTradingResult => {\n  const { token } = useAuth();\n  \n  // State\n  const [openOrders, setOpenOrders] = useState<Order[]>([]);\n  const [orderHistory, setOrderHistory] = useState<Order[]>([]);\n  const [positions, setPositions] = useState<Position[]>([]);\n  const [marketData, setMarketData] = useState<{\n    price: number;\n    priceChange24h: number;\n    volume24h: number;\n    marketCap: number;\n    liquidity: number;\n  } | null>(null);\n  \n  // Loading states\n  const [ordersLoading, setOrdersLoading] = useState(false);\n  const [positionsLoading, setPositionsLoading] = useState(false);\n  const [marketDataLoading, setMarketDataLoading] = useState(false);\n  \n  // Error states\n  const [ordersError, setOrdersError] = useState<string | null>(null);\n  const [positionsError, setPositionsError] = useState<string | null>(null);\n  const [marketDataError, setMarketDataError] = useState<string | null>(null);\n\n  // Fetch orders\n  const refreshOrders = useCallback(async () => {\n    if (!token) return;\n    \n    setOrdersLoading(true);\n    setOrdersError(null);\n    \n    try {\n      const [open, history] = await Promise.all([\n        getOpenOrders(token),\n        getOrderHistory(token, 50, 0)\n      ]);\n      \n      setOpenOrders(open);\n      setOrderHistory(history.orders);\n    } catch (error) {\n      console.error('Error fetching orders:', error);\n      setOrdersError('Failed to fetch orders');\n    } finally {\n      setOrdersLoading(false);\n    }\n  }, [token]);\n\n  // Fetch positions\n  const refreshPositions = useCallback(async () => {\n    if (!token) return;\n    \n    setPositionsLoading(true);\n    setPositionsError(null);\n    \n    try {\n      const positions = await getPositions(token);\n      setPositions(positions);\n    } catch (error) {\n      console.error('Error fetching positions:', error);\n      setPositionsError('Failed to fetch positions');\n    } finally {\n      setPositionsLoading(false);\n    }\n  }, [token]);\n\n  // Fetch market data\n  const refreshMarketData = useCallback(async () => {\n    if (!token || !tokenAddress) return;\n    \n    setMarketDataLoading(true);\n    setMarketDataError(null);\n    \n    try {\n      const data = await getMarketData(token, tokenAddress, chainId);\n      setMarketData(data);\n    } catch (error) {\n      console.error('Error fetching market data:', error);\n      setMarketDataError('Failed to fetch market data');\n    } finally {\n      setMarketDataLoading(false);\n    }\n  }, [token, tokenAddress, chainId]);\n\n  // Refresh all data\n  const refreshAll = useCallback(async () => {\n    await Promise.all([\n      refreshOrders(),\n      refreshPositions(),\n      refreshMarketData()\n    ]);\n  }, [refreshOrders, refreshPositions, refreshMarketData]);\n\n  // Initial fetch\n  useEffect(() => {\n    if (token) {\n      refreshOrders();\n      refreshPositions();\n    }\n  }, [token, refreshOrders, refreshPositions]);\n\n  // Fetch market data when token address changes\n  useEffect(() => {\n    if (token && tokenAddress) {\n      refreshMarketData();\n    }\n  }, [token, tokenAddress, chainId, refreshMarketData]);\n\n  // Set up polling\n  useEffect(() => {\n    if (!token || pollingInterval <= 0) return;\n\n    const interval = setInterval(() => {\n      refreshOrders();\n      refreshPositions();\n      if (tokenAddress) {\n        refreshMarketData();\n      }\n    }, pollingInterval);\n\n    return () => clearInterval(interval);\n  }, [token, pollingInterval, refreshOrders, refreshPositions, refreshMarketData, tokenAddress]);\n\n  return {\n    // Data\n    openOrders,\n    orderHistory,\n    positions,\n    marketData,\n    \n    // Loading states\n    ordersLoading,\n    positionsLoading,\n    marketDataLoading,\n    \n    // Error states\n    ordersError,\n    positionsError,\n    marketDataError,\n    \n    // Actions\n    refreshOrders,\n    refreshPositions,\n    refreshMarketData,\n    refreshAll\n  };\n};\n\nexport default useTrading;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAiDO,MAAM,aAAa,CAAC,EACzB,YAAY,EACZ,UAAU,CAAC,EACX,kBAAkB,KAAK,EACL,GAAG,CAAC,CAAC;;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAExB,QAAQ;IACR,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAMjC;IAEV,iBAAiB;IACjB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,eAAe;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,eAAe;IACf,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAChC,IAAI,CAAC,OAAO;YAEZ,iBAAiB;YACjB,eAAe;YAEf,IAAI;gBACF,MAAM,CAAC,MAAM,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACxC,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD,EAAE;oBACd,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,IAAI;iBAC5B;gBAED,cAAc;gBACd,gBAAgB,QAAQ,MAAM;YAChC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,eAAe;YACjB,SAAU;gBACR,iBAAiB;YACnB;QACF;gDAAG;QAAC;KAAM;IAEV,kBAAkB;IAClB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YACnC,IAAI,CAAC,OAAO;YAEZ,oBAAoB;YACpB,kBAAkB;YAElB,IAAI;gBACF,MAAM,YAAY,MAAM,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE;gBACrC,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,kBAAkB;YACpB,SAAU;gBACR,oBAAoB;YACtB;QACF;mDAAG;QAAC;KAAM;IAEV,oBAAoB;IACpB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YACpC,IAAI,CAAC,SAAS,CAAC,cAAc;YAE7B,qBAAqB;YACrB,mBAAmB;YAEnB,IAAI;gBACF,MAAM,OAAO,MAAM,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,cAAc;gBACtD,cAAc;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,mBAAmB;YACrB,SAAU;gBACR,qBAAqB;YACvB;QACF;oDAAG;QAAC;QAAO;QAAc;KAAQ;IAEjC,mBAAmB;IACnB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE;YAC7B,MAAM,QAAQ,GAAG,CAAC;gBAChB;gBACA;gBACA;aACD;QACH;6CAAG;QAAC;QAAe;QAAkB;KAAkB;IAEvD,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,OAAO;gBACT;gBACA;YACF;QACF;+BAAG;QAAC;QAAO;QAAe;KAAiB;IAE3C,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,SAAS,cAAc;gBACzB;YACF;QACF;+BAAG;QAAC;QAAO;QAAc;QAAS;KAAkB;IAEpD,iBAAiB;IACjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,SAAS,mBAAmB,GAAG;YAEpC,MAAM,WAAW;iDAAY;oBAC3B;oBACA;oBACA,IAAI,cAAc;wBAChB;oBACF;gBACF;gDAAG;YAEH;wCAAO,IAAM,cAAc;;QAC7B;+BAAG;QAAC;QAAO;QAAiB;QAAe;QAAkB;QAAmB;KAAa;IAE7F,OAAO;QACL,OAAO;QACP;QACA;QACA;QACA;QAEA,iBAAiB;QACjB;QACA;QACA;QAEA,eAAe;QACf;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;IACF;AACF;GAtJa;;QAKO,kIAAA,CAAA,UAAO;;;uCAmJZ", "debugId": null}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Trade/OpenPosition/index.tsx"], "sourcesContent": ["import { motion } from 'framer-motion';\r\nimport { useTrading } from '@/hooks/useTrading';\r\nimport { FiTrendingUp, FiTrendingDown } from 'react-icons/fi';\r\n\r\nexport default function OpenPositions() {\r\n    const { positions, positionsLoading } = useTrading({\r\n        pollingInterval: 30000\r\n    });\r\n\r\n    const formatCurrency = (value: number): string => {\r\n        return new Intl.NumberFormat('en-US', {\r\n            style: 'currency',\r\n            currency: 'USD',\r\n            minimumFractionDigits: 2,\r\n            maximumFractionDigits: 2,\r\n        }).format(value);\r\n    };\r\n\r\n    const formatPercent = (value: number): string => {\r\n        const sign = value >= 0 ? '+' : '';\r\n        return `${sign}${value.toFixed(2)}%`;\r\n    };\r\n\r\n    const getTotalPnL = () => {\r\n        return positions.reduce((total, position) => total + position.pnl, 0);\r\n    };\r\n\r\n    return (\r\n        <div className=\"row-span-1 w-full p-3 min-h-[30%] overflow-auto scrollbar-hidden border-b border-white/20\">\r\n            <h5 className=\"font-space-grotesk text-xs font-bold mb-3\">Open Positions</h5>\r\n\r\n            {positionsLoading ? (\r\n                <div className=\"flex items-center justify-center h-full\">\r\n                    <div className=\"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\r\n                </div>\r\n            ) : positions.length > 0 ? (\r\n                <div className=\"space-y-2\">\r\n                    {/* Summary */}\r\n                    <div className=\"bg-black/40 border border-white/10 rounded p-2 mb-3\">\r\n                        <div className=\"flex items-center justify-between text-xs\">\r\n                            <span className=\"text-white/60\">Total PnL</span>\r\n                            <span className={`font-semibold ${getTotalPnL() >= 0 ? 'text-green-400' : 'text-red-400'\r\n                                }`}>\r\n                                {formatCurrency(getTotalPnL())}\r\n                            </span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Positions List */}\r\n                    <div className=\"space-y-1 max-h-32 overflow-y-auto scrollbar-hidden\">\r\n                        {positions.slice(0, 3).map((position) => (\r\n                            <div\r\n                                key={position.id}\r\n                                className=\"bg-black/20 border border-white/5 rounded p-2 hover:border-white/10 transition-colors\"\r\n                            >\r\n                                <div className=\"flex items-center justify-between\">\r\n                                    <div>\r\n                                        <div className=\"text-xs font-medium text-white\">\r\n                                            {position.tokenSymbol}\r\n                                        </div>\r\n                                        <div className=\"text-xs text-white/60\">\r\n                                            {parseFloat(position.amount).toFixed(2)}\r\n                                        </div>\r\n                                    </div>\r\n                                    <div className=\"text-right\">\r\n                                        <div className={`text-xs font-semibold ${position.pnl >= 0 ? 'text-green-400' : 'text-red-400'\r\n                                            }`}>\r\n                                            {formatPercent(position.pnlPercent)}\r\n                                        </div>\r\n                                        <div className=\"text-xs text-white/60\">\r\n                                            {formatCurrency(position.pnl)}\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        ))}\r\n\r\n                        {positions.length > 3 && (\r\n                            <div className=\"text-center text-xs text-white/60 py-1\">\r\n                                +{positions.length - 3} more positions\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <div className='flex flex-col gap-3 h-full justify-center items-center'>\r\n                    <p className=\"text-white/50 text-xs\">No open positions</p>\r\n                    <motion.button\r\n                        className=\"font-orbitron font-semibold bg-white text-black px-6 py-2 rounded-md hover:shadow-lg hover:shadow-white/20 w-full text-xs\"\r\n                        whileHover={{\r\n                            boxShadow: \"0 0 15px rgba(255, 255, 255, 0.5)\",\r\n                            scale: 1.05\r\n                        }}\r\n                        whileTap={{\r\n                            backgroundColor: \"#f0f0f0\",\r\n                            scale: 1.03\r\n                        }}\r\n                    >\r\n                        Start Trading\r\n                    </motion.button>\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAGe,SAAS;;IACpB,MAAM,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE;QAC/C,iBAAiB;IACrB;IAEA,MAAM,iBAAiB,CAAC;QACpB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YAClC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QAC3B,GAAG,MAAM,CAAC;IACd;IAEA,MAAM,gBAAgB,CAAC;QACnB,MAAM,OAAO,SAAS,IAAI,MAAM;QAChC,OAAO,GAAG,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;IACxC;IAEA,MAAM,cAAc;QAChB,OAAO,UAAU,MAAM,CAAC,CAAC,OAAO,WAAa,QAAQ,SAAS,GAAG,EAAE;IACvE;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAG,WAAU;0BAA4C;;;;;;YAEzD,iCACG,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;uBAEnB,UAAU,MAAM,GAAG,kBACnB,6LAAC;gBAAI,WAAU;;kCAEX,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;oCAAK,WAAW,CAAC,cAAc,EAAE,iBAAiB,IAAI,mBAAmB,gBACpE;8CACD,eAAe;;;;;;;;;;;;;;;;;kCAM5B,6LAAC;wBAAI,WAAU;;4BACV,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBACxB,6LAAC;oCAEG,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;;kEACG,6LAAC;wDAAI,WAAU;kEACV,SAAS,WAAW;;;;;;kEAEzB,6LAAC;wDAAI,WAAU;kEACV,WAAW,SAAS,MAAM,EAAE,OAAO,CAAC;;;;;;;;;;;;0DAG7C,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAI,WAAW,CAAC,sBAAsB,EAAE,SAAS,GAAG,IAAI,IAAI,mBAAmB,gBAC1E;kEACD,cAAc,SAAS,UAAU;;;;;;kEAEtC,6LAAC;wDAAI,WAAU;kEACV,eAAe,SAAS,GAAG;;;;;;;;;;;;;;;;;;mCAlBnC,SAAS,EAAE;;;;;4BAyBvB,UAAU,MAAM,GAAG,mBAChB,6LAAC;gCAAI,WAAU;;oCAAyC;oCAClD,UAAU,MAAM,GAAG;oCAAE;;;;;;;;;;;;;;;;;;qCAMvC,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACV,WAAU;wBACV,YAAY;4BACR,WAAW;4BACX,OAAO;wBACX;wBACA,UAAU;4BACN,iBAAiB;4BACjB,OAAO;wBACX;kCACH;;;;;;;;;;;;;;;;;;AAOrB;GApGwB;;QACoB,6HAAA,CAAA,aAAU;;;KAD9B", "debugId": null}}, {"offset": {"line": 1284, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/services/api/dexscreener.ts"], "sourcesContent": ["import axios, { type AxiosError } from 'axios';\r\n\r\ninterface Token {\r\n    address: string;\r\n    name: string;\r\n    symbol: string;\r\n}\r\n\r\ninterface PairInfo {\r\n    imageUrl: string;\r\n    websites: { url: string }[];\r\n    socials: { platform: string; handle: string }[];\r\n}\r\n\r\ninterface Pair {\r\n    chainId: string;\r\n    dexId: string;\r\n    url: string;\r\n    pairAddress: string;\r\n    labels: string[];\r\n    baseToken: Token;\r\n    quoteToken: Token;\r\n    priceNative: string;\r\n    priceUsd: string;\r\n    txns: Record<string, { buys: number; sells: number }>;\r\n    volume: Record<string, number>;\r\n    priceChange: Record<string, number>;\r\n    liquidity: { usd: number; base: number; quote: number };\r\n    fdv: number;\r\n    marketCap: number;\r\n    pairCreatedAt: number;\r\n    info: PairInfo;\r\n    boosts: { active: number };\r\n}\r\n\r\ninterface DexScreenerResponse {\r\n    pairs: Pair[];\r\n}\r\n\r\nconst MAX_RETRIES = 3;\r\nconst INITIAL_DELAY = 1000;\r\n\r\nconst delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\r\n\r\nexport const getTokenPairs = async (\r\n    tokenAddress: `0x${string}`,\r\n    network: string,\r\n    retries = MAX_RETRIES\r\n): Promise<DexScreenerResponse> => {\r\n    // Get Pools First\r\n    const poolsEndpoint = `https://api.dexscreener.com/token-pairs/v1/${network}/${tokenAddress}`\r\n\r\n    try {\r\n        const response = await axios.get(poolsEndpoint);\r\n        if (!response) {\r\n            throw new Error(\"Failed to fetch pools\");\r\n        }\r\n\r\n        const url = `https://api.dexscreener.com/latest/dex/pairs/${network}/${response.data[0].pairAddress}`;\r\n\r\n        try {\r\n            const response = await axios.get<DexScreenerResponse>(url);\r\n            return response.data;\r\n        } catch (error) {\r\n            const axiosError = error as AxiosError;\r\n\r\n            if (axiosError.response?.status === 429 && retries > 0) {\r\n                const delayTime = INITIAL_DELAY * (MAX_RETRIES - retries + 1);\r\n                await delay(delayTime);\r\n                return getTokenPairs(tokenAddress, network, retries - 1);\r\n            }\r\n\r\n            console.error(\"Error fetching token pairs:\", error);\r\n            throw error;\r\n        }\r\n    } catch (error) {\r\n        console.error(\"Error fetching pools:\", error);\r\n        throw error;\r\n    }\r\n\r\n}\r\n\r\nexport const getTokenData = async (tokenAddress: `0x${string}`, network: string): Promise<Pair | null> => {\r\n    const url = `https://api.dexscreener.com/token-pairs/v1/${network}/${tokenAddress}`;\r\n    try {\r\n        const response = await axios.get(url);\r\n        return response.data[0];\r\n    } catch (error) {\r\n        console.error(\"Error fetching token data:\", error);\r\n        throw error;\r\n    }\r\n}"], "names": [], "mappings": ";;;;AAAA;;AAuCA,MAAM,cAAc;AACpB,MAAM,gBAAgB;AAEtB,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAElE,MAAM,gBAAgB,OACzB,cACA,SACA,UAAU,WAAW;IAErB,kBAAkB;IAClB,MAAM,gBAAgB,CAAC,2CAA2C,EAAE,QAAQ,CAAC,EAAE,cAAc;IAE7F,IAAI;QACA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC;QACjC,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,MAAM,CAAC,6CAA6C,EAAE,QAAQ,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE;QAErG,IAAI;YACA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAsB;YACtD,OAAO,SAAS,IAAI;QACxB,EAAE,OAAO,OAAO;YACZ,MAAM,aAAa;YAEnB,IAAI,WAAW,QAAQ,EAAE,WAAW,OAAO,UAAU,GAAG;gBACpD,MAAM,YAAY,gBAAgB,CAAC,cAAc,UAAU,CAAC;gBAC5D,MAAM,MAAM;gBACZ,OAAO,cAAc,cAAc,SAAS,UAAU;YAC1D;YAEA,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACV;IACJ,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACV;AAEJ;AAEO,MAAM,eAAe,OAAO,cAA6B;IAC5D,MAAM,MAAM,CAAC,2CAA2C,EAAE,QAAQ,CAAC,EAAE,cAAc;IACnF,IAAI;QACA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC;QACjC,OAAO,SAAS,IAAI,CAAC,EAAE;IAC3B,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACV;AACJ", "debugId": null}}, {"offset": {"line": 1339, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/hooks/useTokenPairs.ts"], "sourcesContent": ["import axios, { AxiosError } from 'axios';\r\nimport { useState, useCallback, useEffect } from 'react';\r\nimport { getTokenPairs } from '../services/api/dexscreener';\r\nimport { getTokenData } from '@/services/api/flooz';\r\n\r\ntype UseTokenPairsReturn = {\r\n    data: any | null;\r\n    tokenData: any | null;\r\n    loading: boolean;\r\n    error: AxiosError | null;\r\n    fetchTokenPairs: () => Promise<void>;\r\n};\r\n\r\nexport const useTokenPairs = (tokenAddress: `0x${string}`, network: string): UseTokenPairsReturn => {\r\n    const [data, setData] = useState<any | null>(null);\r\n    const [tokenData, setTokenData] = useState<any | null>(null);\r\n    const [loading, setLoading] = useState<boolean>(true);\r\n    const [error, setError] = useState<AxiosError | null>(null);\r\n\r\n    const fetchTokenPairs = useCallback(async () => {\r\n        setLoading(true);\r\n        setError(null);\r\n\r\n        try {\r\n            const allResults = await Promise.all([\r\n                getTokenData(network, tokenAddress),\r\n                getTokenPairs(tokenAddress, network),\r\n            ]);\r\n            setData(allResults[1]);\r\n            setTokenData(allResults[0].results[0]);\r\n        } catch (error) {\r\n            setError(error as AxiosError);\r\n            setData(null);\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, [tokenAddress, network]);\r\n\r\n    useEffect(() => {\r\n        if (tokenAddress && network) {\r\n            fetchTokenPairs();\r\n        }\r\n    }, [fetchTokenPairs]);\r\n\r\n    return {\r\n        data,\r\n        tokenData,\r\n        loading,\r\n        error,\r\n        fetchTokenPairs,\r\n    };\r\n}; "], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;;AAUO,MAAM,gBAAgB,CAAC,cAA6B;;IACvD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAEtD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAChC,WAAW;YACX,SAAS;YAET,IAAI;gBACA,MAAM,aAAa,MAAM,QAAQ,GAAG,CAAC;oBACjC,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE,SAAS;oBACtB,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE,cAAc;iBAC/B;gBACD,QAAQ,UAAU,CAAC,EAAE;gBACrB,aAAa,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;YACzC,EAAE,OAAO,OAAO;gBACZ,SAAS;gBACT,QAAQ;YACZ,SAAU;gBACN,WAAW;YACf;QACJ;qDAAG;QAAC;QAAc;KAAQ;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,IAAI,gBAAgB,SAAS;gBACzB;YACJ;QACJ;kCAAG;QAAC;KAAgB;IAEpB,OAAO;QACH;QACA;QACA;QACA;QACA;IACJ;AACJ;GAtCa", "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n    return twMerge(clsx(inputs));\r\n} "], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACtC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACxB", "debugId": null}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/ui/skeleton.tsx"], "sourcesContent": ["\"use client\"\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport function Skeleton({\r\n    className,\r\n    ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n    return (\r\n        <div\r\n            className={cn(\"animate-pulse rounded-md bg-muted\", className)}\r\n            {...props}\r\n        />\r\n    );\r\n} "], "names": [], "mappings": ";;;;AACA;AADA;;;AAGO,SAAS,SAAS,EACrB,SAAS,EACT,GAAG,OACgC;IACnC,qBACI,6LAAC;QACG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGrB;KAVgB", "debugId": null}}, {"offset": {"line": 1453, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Trade/TokenPairData/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion } from 'framer-motion';\r\nimport { useTokenPairs } from '@/hooks/useTokenPairs';\r\nimport { FaStar } from \"react-icons/fa\";\r\nimport { useWeb3 } from \"@/contexts/Web3Context\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { chains, getTimeAgo, getShareLink } from '@/utils/data';\r\nimport { toast } from 'react-toastify';\r\n\r\n\r\nexport default function TokenPairData() {\r\n    const { chain, address } = useParams();\r\n    const { data, tokenData, loading, error, fetchTokenPairs } = useTokenPairs(address as `0x${string}`, chain as string);\r\n    const [favorite, setFavorite] = useState<boolean>(false);\r\n    const tokenChain = chains.find(c => c.slug === chain);\r\n\r\n    // Set page title based on token data\r\n    useEffect(() => {\r\n        if (data?.pair && tokenData?.marketData) {\r\n            const price = Number(data.pair.priceUsd || tokenData.marketData.priceUSD).toFixed(6);\r\n            const priceChange = data.pair.priceChange?.h24 || 0;\r\n            const symbol = data.pair.baseToken?.symbol || 'Token';\r\n            document.title = `${symbol}/${data.pair.quoteToken?.symbol} | $${price} | ${priceChange > 0 ? '↑' : '↓'} ${Math.abs(priceChange).toFixed(1)}% On ${tokenChain?.slug ? tokenChain.slug.charAt(0).toUpperCase() + tokenChain.slug.slice(1) : ''} / ${data?.pair?.dexId ? data.pair.dexId.charAt(0).toUpperCase() + data.pair.dexId.slice(1) : ''} | Anubis Terminal`;\r\n        }\r\n    }, [data, tokenData]);\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className='border-b border-white/20 flex justify-between gap-x-3 py-5 px-3'>\r\n                <Skeleton className=\"w-5 h-5 rounded-full\" />\r\n                <div className=\"flex gap-x-1 items-center\">\r\n                    <Skeleton className=\"w-5 h-5 rounded-full\" />\r\n                    <Skeleton className=\"h-4 w-20\" />\r\n                    <Skeleton className=\"h-3 w-10\" />\r\n                </div>\r\n                <Skeleton className=\"w-3 h-3 rounded-full\" />\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (error) {\r\n        return (\r\n            <div className='border-b border-white/20 flex justify-between gap-x-3 py-5 px-3 text-red-500'>\r\n                Error loading token data\r\n            </div>\r\n        );\r\n    }\r\n\r\n    console.log(\"Data\", data);\r\n    console.log(\"Token Data\", tokenData)\r\n    console.log(\"Token Chain\", tokenChain)\r\n\r\n    function handleCopyShareLink() {\r\n        navigator.clipboard.writeText(getShareLink(chain as string, address as string)).then(() => {\r\n            toast.success(\"Link Copied\");\r\n        }).catch((err) => {\r\n            toast.error(\"Failed to copy link\");\r\n            console.error(\"Failed to copy: \", err);\r\n        });\r\n    }\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-full\">\r\n            {/* Token Info Header - matches image */}\r\n            <div className=\"flex items-center justify-between px-4 py-3 bg-[#181818] border-b border-white/10\">\r\n                {/* Left: Token logo, name, pair, network, DEX, share */}\r\n                <div className=\"flex items-center gap-3 min-w-0\">\r\n                    {/* Favorite Star */}\r\n                    <motion.button\r\n                        whileTap={{ scale: 0.88 }}\r\n                        whileHover={{ cursor: \"pointer\", scale: 1.04 }}\r\n                        onClick={() => setFavorite(!favorite)}\r\n                        className=\"mr-1\"\r\n                    >\r\n                        <FaStar className={`${favorite ? \"text-yellow-400\" : \"text-white/30\"} text-lg`} />\r\n                    </motion.button>\r\n                    {/* Token Logo */}\r\n                    <img\r\n                        src={data?.pair?.info?.imageUrl || tokenData?.details?.imageLargeUrl}\r\n                        alt={data?.pair?.baseToken?.symbol}\r\n                        className=\"w-10 h-10 rounded-full border border-white/10 bg-black object-cover\"\r\n                    />\r\n                    {/* Name and Pair */}\r\n                    <div className=\"flex flex-col min-w-0\">\r\n                        <span className=\"font-orbitron font-bold text-white truncate text-base leading-tight\">\r\n                            {data?.pair?.baseToken?.symbol}/{data?.pair?.quoteToken?.symbol}\r\n                        </span>\r\n                        <div className=\"flex items-center gap-2 text-xs text-white/60 mt-0.5\">\r\n                            {/* Age */}\r\n                            <span>{getTimeAgo(tokenData?.activity?.createdAt || 0)}</span>\r\n                            {/* Network */}\r\n                            <span className=\"flex items-center gap-1\">\r\n                                <img src={tokenChain?.logo} alt=\"network\" className=\"w-4 h-4 inline-block\" />\r\n                                {tokenChain?.chain || data?.pair?.chainId}\r\n                            </span>\r\n                            {/* DEX */}\r\n                            <span className=\"flex items-center gap-1\">\r\n                                <img src=\"/icons/dex.svg\" alt=\"dex\" className=\"w-4 h-4 inline-block\" />\r\n                                {data?.pair?.dexId || 'DEX'}\r\n                            </span>\r\n                            {/* Share */}\r\n                            <button\r\n                                onClick={handleCopyShareLink}\r\n                                className=\"flex items-center gap-1 text-orange-400 hover:underline ml-2 cursor-pointer\"\r\n                            >\r\n                                <svg width=\"16\" height=\"16\" fill=\"none\" viewBox=\"0 0 16 16\"><path d=\"M12.667 10.667v1.333A1.333 1.333 0 0 1 11.333 13.333H4.667A1.333 1.333 0 0 1 3.333 12V5.333A1.333 1.333 0 0 1 4.667 4h1.333\" stroke=\"currentColor\" strokeWidth=\"1.2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" /><path d=\"M10.667 2.667h2.666v2.666\" stroke=\"currentColor\" strokeWidth=\"1.2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" /><path d=\"M7.333 8.667 13.333 2.667\" stroke=\"currentColor\" strokeWidth=\"1.2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" /></svg>\r\n                                <span className=\"font-medium\">Share</span>\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                {/* Right: Market Cap, Price, Liquidity, Taxes */}\r\n                <div className=\"flex items-center gap-8\">\r\n                    <div className=\"flex flex-col items-end\">\r\n                        <span className=\"text-xs text-white/50\">Market Cap</span>\r\n                        <span className=\"font-orbitron font-semibold text-lg text-white\">${Number(data?.pair?.marketCap || tokenData?.marketData?.marketCap).toLocaleString(undefined, { maximumFractionDigits: 2 })}M</span>\r\n                    </div>\r\n                    <div className=\"flex flex-col items-end\">\r\n                        <span className=\"text-xs text-white/50\">Price</span>\r\n                        <span className=\"font-orbitron font-semibold text-lg text-white\">${Number(data?.pair?.priceUsd || tokenData?.marketData?.priceUSD).toFixed(7)}</span>\r\n                    </div>\r\n                    <div className=\"flex flex-col items-end\">\r\n                        <span className=\"text-xs text-white/50\">Liquidity</span>\r\n                        <span className=\"font-orbitron font-semibold text-lg text-white\">${Number(data?.pair?.liquidity?.usd || tokenData?.marketData?.liquidity).toLocaleString(undefined, { maximumFractionDigits: 2 })}</span>\r\n                    </div>\r\n                    <div className=\"flex flex-col items-end\">\r\n                        <span className=\"text-xs text-white/50\">Taxes</span>\r\n                        <span className=\"font-orbitron font-semibold text-lg text-white\">0.0% <span className=\"text-white/40\">|</span> 0.0%</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Chart Placeholder */}\r\n            <div className=\"flex-1 flex flex-col bg-black/40 border-b border-white/10 justify-center items-center min-h-[260px]\">\r\n                <div className=\"w-full h-64 flex items-center justify-center\">\r\n                    <span className=\"text-white/40 text-lg\">[Chart Placeholder]</span>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Tabs */}\r\n            <div className=\"flex border-b border-white/10 bg-black/30\">\r\n                {['Trades', 'Open Orders', 'Order History', 'Liquidity', 'Holders', 'Top Traders'].map(tab => (\r\n                    <button\r\n                        key={tab}\r\n                        className={`px-4 py-2 text-sm font-medium text-white/80 hover:text-white focus:outline-none ${tab === 'Trades' ? 'border-b-2 border-green-500 text-white' : ''}`}\r\n                    >\r\n                        {tab}\r\n                    </button>\r\n                ))}\r\n            </div>\r\n\r\n            {/* Trades Table (Dummy Data) */}\r\n            <div className=\"flex-1 overflow-y-auto bg-black/20\">\r\n                <table className=\"min-w-full text-xs text-left text-white/80\">\r\n                    <thead className=\"bg-black/40 border-b border-white/10\">\r\n                        <tr>\r\n                            <th className=\"px-3 py-2 font-semibold\">Age</th>\r\n                            <th className=\"px-3 py-2 font-semibold\">Side</th>\r\n                            <th className=\"px-3 py-2 font-semibold\">MCap</th>\r\n                            <th className=\"px-3 py-2 font-semibold\">Amount</th>\r\n                            <th className=\"px-3 py-2 font-semibold\">Total USD</th>\r\n                            <th className=\"px-3 py-2 font-semibold\">Total USDT</th>\r\n                            <th className=\"px-3 py-2 font-semibold\">Maker</th>\r\n                        </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                        {[\r\n                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '195.2', totalUsd: '399.21', totalUsdt: '399.1553', maker: '186Cf5a' },\r\n                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '244.5', totalUsd: '500.07', totalUsdt: '500.000', maker: '7eeF25A9' },\r\n                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '760.9', totalUsd: '1,556.51', totalUsdt: '1,556.2861', maker: 'A1058569' },\r\n                            { age: '3s', side: 'Sell', mcap: '204.6M', amount: '37.8', totalUsd: '77.37', totalUsdt: '77.3585', maker: '67d1C5d8' },\r\n                            { age: '3s', side: 'Sell', mcap: '204.6M', amount: '1.01K', totalUsd: '2,059.90', totalUsdt: '2,059.6103', maker: '770Fa894' },\r\n                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '1.61K', totalUsd: '3,300.47', totalUsdt: '3,300.000', maker: '3243B0C4' },\r\n                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '801.8', totalUsd: '1,640.23', totalUsdt: '1,640.000', maker: '7F7355BF' },\r\n                        ].map((row, i) => (\r\n                            <tr key={i} className=\"border-b border-white/5 last:border-0\">\r\n                                <td className=\"px-3 py-2 whitespace-nowrap\">{row.age}</td>\r\n                                <td className={`px-3 py-2 whitespace-nowrap font-bold ${row.side === 'Buy' ? 'text-green-400' : 'text-red-400'}`}>{row.side}</td>\r\n                                <td className=\"px-3 py-2 whitespace-nowrap\">{row.mcap}</td>\r\n                                <td className=\"px-3 py-2 whitespace-nowrap\">{row.amount}</td>\r\n                                <td className=\"px-3 py-2 whitespace-nowrap\">{row.totalUsd}</td>\r\n                                <td className=\"px-3 py-2 whitespace-nowrap\">{row.totalUsdt}</td>\r\n                                <td className=\"px-3 py-2 whitespace-nowrap\">{row.maker}</td>\r\n                            </tr>\r\n                        ))}\r\n                    </tbody>\r\n                </table>\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAYe,SAAS;;IACpB,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACnC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD,EAAE,SAA0B;IACrG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClD,MAAM,aAAa,uHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;IAE/C,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,IAAI,MAAM,QAAQ,WAAW,YAAY;gBACrC,MAAM,QAAQ,OAAO,KAAK,IAAI,CAAC,QAAQ,IAAI,UAAU,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC;gBAClF,MAAM,cAAc,KAAK,IAAI,CAAC,WAAW,EAAE,OAAO;gBAClD,MAAM,SAAS,KAAK,IAAI,CAAC,SAAS,EAAE,UAAU;gBAC9C,SAAS,KAAK,GAAG,GAAG,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,EAAE,OAAO,IAAI,EAAE,MAAM,GAAG,EAAE,cAAc,IAAI,MAAM,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,aAAa,OAAO,CAAC,GAAG,KAAK,EAAE,YAAY,OAAO,WAAW,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,EAAE,MAAM,MAAM,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,kBAAkB,CAAC;YACtW;QACJ;kCAAG;QAAC;QAAM;KAAU;IAEpB,IAAI,SAAS;QACT,qBACI,6LAAC;YAAI,WAAU;;8BACX,6LAAC,uIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;8BACpB,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;8BAExB,6LAAC,uIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;;IAGhC;IAEA,IAAI,OAAO;QACP,qBACI,6LAAC;YAAI,WAAU;sBAA+E;;;;;;IAItG;IAEA,QAAQ,GAAG,CAAC,QAAQ;IACpB,QAAQ,GAAG,CAAC,cAAc;IAC1B,QAAQ,GAAG,CAAC,eAAe;IAE3B,SAAS;QACL,UAAU,SAAS,CAAC,SAAS,CAAC,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD,EAAE,OAAiB,UAAoB,IAAI,CAAC;YACjF,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAClB,GAAG,KAAK,CAAC,CAAC;YACN,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,oBAAoB;QACtC;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC;gBAAI,WAAU;;kCAEX,6LAAC;wBAAI,WAAU;;0CAEX,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACV,UAAU;oCAAE,OAAO;gCAAK;gCACxB,YAAY;oCAAE,QAAQ;oCAAW,OAAO;gCAAK;gCAC7C,SAAS,IAAM,YAAY,CAAC;gCAC5B,WAAU;0CAEV,cAAA,6LAAC,iJAAA,CAAA,SAAM;oCAAC,WAAW,GAAG,WAAW,oBAAoB,gBAAgB,QAAQ,CAAC;;;;;;;;;;;0CAGlF,6LAAC;gCACG,KAAK,MAAM,MAAM,MAAM,YAAY,WAAW,SAAS;gCACvD,KAAK,MAAM,MAAM,WAAW;gCAC5B,WAAU;;;;;;0CAGd,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAK,WAAU;;4CACX,MAAM,MAAM,WAAW;4CAAO;4CAAE,MAAM,MAAM,YAAY;;;;;;;kDAE7D,6LAAC;wCAAI,WAAU;;0DAEX,6LAAC;0DAAM,CAAA,GAAA,uHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,UAAU,aAAa;;;;;;0DAEpD,6LAAC;gDAAK,WAAU;;kEACZ,6LAAC;wDAAI,KAAK,YAAY;wDAAM,KAAI;wDAAU,WAAU;;;;;;oDACnD,YAAY,SAAS,MAAM,MAAM;;;;;;;0DAGtC,6LAAC;gDAAK,WAAU;;kEACZ,6LAAC;wDAAI,KAAI;wDAAiB,KAAI;wDAAM,WAAU;;;;;;oDAC7C,MAAM,MAAM,SAAS;;;;;;;0DAG1B,6LAAC;gDACG,SAAS;gDACT,WAAU;;kEAEV,6LAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,MAAK;wDAAO,SAAQ;;0EAAY,6LAAC;gEAAK,GAAE;gEAA8H,QAAO;gEAAe,aAAY;gEAAM,eAAc;gEAAQ,gBAAe;;;;;;0EAAU,6LAAC;gEAAK,GAAE;gEAA4B,QAAO;gEAAe,aAAY;gEAAM,eAAc;gEAAQ,gBAAe;;;;;;0EAAU,6LAAC;gEAAK,GAAE;gEAA4B,QAAO;gEAAe,aAAY;gEAAM,eAAc;gEAAQ,gBAAe;;;;;;;;;;;;kEACrgB,6LAAC;wDAAK,WAAU;kEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM9C,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;;4CAAiD;4CAAE,OAAO,MAAM,MAAM,aAAa,WAAW,YAAY,WAAW,cAAc,CAAC,WAAW;gDAAE,uBAAuB;4CAAE;4CAAG;;;;;;;;;;;;;0CAEjM,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;;4CAAiD;4CAAE,OAAO,MAAM,MAAM,YAAY,WAAW,YAAY,UAAU,OAAO,CAAC;;;;;;;;;;;;;0CAE/I,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;;4CAAiD;4CAAE,OAAO,MAAM,MAAM,WAAW,OAAO,WAAW,YAAY,WAAW,cAAc,CAAC,WAAW;gDAAE,uBAAuB;4CAAE;;;;;;;;;;;;;0CAEnM,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;;4CAAiD;0DAAK,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;4CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;0BAM1H,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;;;;;0BAKhD,6LAAC;gBAAI,WAAU;0BACV;oBAAC;oBAAU;oBAAe;oBAAiB;oBAAa;oBAAW;iBAAc,CAAC,GAAG,CAAC,CAAA,oBACnF,6LAAC;wBAEG,WAAW,CAAC,gFAAgF,EAAE,QAAQ,WAAW,2CAA2C,IAAI;kCAE/J;uBAHI;;;;;;;;;;0BASjB,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAM,WAAU;;sCACb,6LAAC;4BAAM,WAAU;sCACb,cAAA,6LAAC;;kDACG,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;;;;;;;;;;;;sCAGhD,6LAAC;sCACI;gCACG;oCAAE,KAAK;oCAAM,MAAM;oCAAO,MAAM;oCAAU,QAAQ;oCAAS,UAAU;oCAAU,WAAW;oCAAY,OAAO;gCAAU;gCACvH;oCAAE,KAAK;oCAAM,MAAM;oCAAO,MAAM;oCAAU,QAAQ;oCAAS,UAAU;oCAAU,WAAW;oCAAW,OAAO;gCAAW;gCACvH;oCAAE,KAAK;oCAAM,MAAM;oCAAO,MAAM;oCAAU,QAAQ;oCAAS,UAAU;oCAAY,WAAW;oCAAc,OAAO;gCAAW;gCAC5H;oCAAE,KAAK;oCAAM,MAAM;oCAAQ,MAAM;oCAAU,QAAQ;oCAAQ,UAAU;oCAAS,WAAW;oCAAW,OAAO;gCAAW;gCACtH;oCAAE,KAAK;oCAAM,MAAM;oCAAQ,MAAM;oCAAU,QAAQ;oCAAS,UAAU;oCAAY,WAAW;oCAAc,OAAO;gCAAW;gCAC7H;oCAAE,KAAK;oCAAM,MAAM;oCAAO,MAAM;oCAAU,QAAQ;oCAAS,UAAU;oCAAY,WAAW;oCAAa,OAAO;gCAAW;gCAC3H;oCAAE,KAAK;oCAAM,MAAM;oCAAO,MAAM;oCAAU,QAAQ;oCAAS,UAAU;oCAAY,WAAW;oCAAa,OAAO;gCAAW;6BAC9H,CAAC,GAAG,CAAC,CAAC,KAAK,kBACR,6LAAC;oCAAW,WAAU;;sDAClB,6LAAC;4CAAG,WAAU;sDAA+B,IAAI,GAAG;;;;;;sDACpD,6LAAC;4CAAG,WAAW,CAAC,sCAAsC,EAAE,IAAI,IAAI,KAAK,QAAQ,mBAAmB,gBAAgB;sDAAG,IAAI,IAAI;;;;;;sDAC3H,6LAAC;4CAAG,WAAU;sDAA+B,IAAI,IAAI;;;;;;sDACrD,6LAAC;4CAAG,WAAU;sDAA+B,IAAI,MAAM;;;;;;sDACvD,6LAAC;4CAAG,WAAU;sDAA+B,IAAI,QAAQ;;;;;;sDACzD,6LAAC;4CAAG,WAAU;sDAA+B,IAAI,SAAS;;;;;;sDAC1D,6LAAC;4CAAG,WAAU;sDAA+B,IAAI,KAAK;;;;;;;mCAPjD;;;;;;;;;;;;;;;;;;;;;;;;;;;AAerC;GApLwB;;QACO,qIAAA,CAAA,YAAS;QACyB,gIAAA,CAAA,gBAAa;;;KAFtD", "debugId": null}}, {"offset": {"line": 2187, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Trade/TradingPanel/index.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FiTrendingUp, FiTrendingDown, FiSettings, FiInfo } from 'react-icons/fi';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { executeTrade, executeSell, getTradingFees, type TradeParams, type SellParams } from '@/services/bot/trading';\nimport { useTrading } from '@/hooks/useTrading';\nimport { toast } from 'react-toastify';\n\ninterface TradingPanelProps {\n  tokenAddress?: string;\n  tokenSymbol?: string;\n  tokenName?: string;\n  currentPrice?: number;\n  chainId?: number;\n}\n\ntype OrderType = 'market' | 'limit';\ntype TradeType = 'buy' | 'sell';\n\nexport default function TradingPanel({\n  tokenAddress,\n  tokenSymbol = 'TOKEN',\n  tokenName = 'Token',\n  currentPrice = 0,\n  chainId = 1\n}: TradingPanelProps) {\n  const { token } = useAuth();\n  const { marketData, marketDataLoading } = useTrading({\n    tokenAddress,\n    chainId,\n    pollingInterval: 10000\n  });\n  const [tradeType, setTradeType] = useState<TradeType>('buy');\n  const [orderType, setOrderType] = useState<OrderType>('market');\n  const [amount, setAmount] = useState<string>('');\n  const [limitPrice, setLimitPrice] = useState<string>('');\n  const [slippage, setSlippage] = useState<number>(0.5);\n  const [sellPercent, setSellPercent] = useState<number>(25);\n  const [isLoading, setIsLoading] = useState(false);\n  const [fees, setFees] = useState({\n    gasFee: '0.001',\n    tradingFee: '0.003',\n    slippageFee: '0.001',\n    totalFee: '0.005'\n  });\n\n  // Quick amount buttons for buy orders\n  const quickAmounts = [50, 100, 500, 1000];\n\n  // Quick percentage buttons for sell orders\n  const quickPercentages = [25, 50, 75, 100];\n\n  // Use market data price if available, otherwise fallback to currentPrice\n  const displayPrice = marketData?.price || currentPrice;\n\n  // Update limit price when price changes\n  useEffect(() => {\n    if (orderType === 'limit' && displayPrice > 0) {\n      setLimitPrice(displayPrice.toString());\n    }\n  }, [displayPrice, orderType]);\n\n  // Fetch trading fees when amount changes\n  useEffect(() => {\n    if (token && tokenAddress && amount && parseFloat(amount) > 0) {\n      const fetchFees = async () => {\n        try {\n          const tradeParams: TradeParams = {\n            token: tokenAddress,\n            amount: parseFloat(amount),\n            orderType,\n            limitPrice: orderType === 'limit' ? parseFloat(limitPrice) : undefined,\n            slippage\n          };\n          const feeData = await getTradingFees(token, tradeParams);\n          setFees(feeData);\n        } catch (error) {\n          console.error('Error fetching fees:', error);\n        }\n      };\n\n      const debounceTimer = setTimeout(fetchFees, 500);\n      return () => clearTimeout(debounceTimer);\n    }\n  }, [token, tokenAddress, amount, orderType, limitPrice, slippage]);\n\n  const handleTrade = async () => {\n    if (!token || !tokenAddress) {\n      toast.error('Please connect your wallet and select a token');\n      return;\n    }\n\n    if (!amount || parseFloat(amount) <= 0) {\n      toast.error('Please enter a valid amount');\n      return;\n    }\n\n    if (orderType === 'limit' && (!limitPrice || parseFloat(limitPrice) <= 0)) {\n      toast.error('Please enter a valid limit price');\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      if (tradeType === 'buy') {\n        const tradeParams: TradeParams = {\n          token: tokenAddress,\n          amount: parseFloat(amount),\n          orderType,\n          limitPrice: orderType === 'limit' ? parseFloat(limitPrice) : undefined,\n          slippage\n        };\n\n        const result = await executeTrade(token, tradeParams);\n        if (result.success) {\n          toast.success(`Buy order ${orderType === 'market' ? 'executed' : 'placed'} successfully!`);\n          setAmount('');\n        } else {\n          toast.error(result.message || 'Trade failed');\n        }\n      } else {\n        const sellParams: SellParams = {\n          token: tokenAddress,\n          percent: sellPercent,\n          orderType,\n          limitPrice: orderType === 'limit' ? parseFloat(limitPrice) : undefined\n        };\n\n        const result = await executeSell(token, sellParams);\n        if (result.success) {\n          toast.success(`Sell order ${orderType === 'market' ? 'executed' : 'placed'} successfully!`);\n          setSellPercent(25);\n        } else {\n          toast.error(result.message || 'Sell failed');\n        }\n      }\n    } catch (error) {\n      console.error('Trade error:', error);\n      toast.error('Trade failed. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleQuickAmount = (value: number) => {\n    setAmount(value.toString());\n  };\n\n  const handleQuickPercentage = (value: number) => {\n    setSellPercent(value);\n  };\n\n  return (\n    <div className=\"h-full flex flex-col bg-black/50 border border-white/10 rounded-md p-4 gap-4\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"font-orbitron font-semibold text-white text-sm\">\n          Trade {tokenSymbol}\n        </h3>\n        <button className=\"p-1.5 rounded-lg hover:bg-white/10 text-white/60 hover:text-white transition-colors\">\n          <FiSettings size={16} />\n        </button>\n      </div>\n\n      {/* Trade Type Tabs */}\n      <div className=\"flex gap-2 mb-3\">\n        <button\n          onClick={() => setTradeType('buy')}\n          className={`flex-1 font-orbitron font-semibold py-2 rounded-md transition-colors ${tradeType === 'buy'\n            ? 'bg-green-600 text-white'\n            : 'border border-white/10 bg-white/5 text-white hover:bg-white/10'\n            }`}\n        >\n          <FiTrendingUp className=\"inline mr-1\" size={14} />\n          Buy\n        </button>\n        <button\n          onClick={() => setTradeType('sell')}\n          className={`flex-1 font-orbitron font-semibold py-2 rounded-md transition-colors ${tradeType === 'sell'\n            ? 'bg-red-600 text-white'\n            : 'border border-white/10 bg-white/5 text-white hover:bg-white/10'\n            }`}\n        >\n          <FiTrendingDown className=\"inline mr-1\" size={14} />\n          Sell\n        </button>\n      </div>\n\n      {/* Order Type */}\n      <div className=\"flex gap-2 mb-3\">\n        <button\n          onClick={() => setOrderType('market')}\n          className={`flex-1 font-orbitron font-semibold py-1.5 rounded-md text-xs transition-colors ${orderType === 'market'\n            ? 'bg-white/20 text-white'\n            : 'border border-white/10 bg-white/5 text-white/80 hover:bg-white/10'\n            }`}\n        >\n          Market\n        </button>\n        <button\n          onClick={() => setOrderType('limit')}\n          className={`flex-1 font-orbitron font-semibold py-1.5 rounded-md text-xs transition-colors ${orderType === 'limit'\n            ? 'bg-white/20 text-white'\n            : 'border border-white/10 bg-white/5 text-white/80 hover:bg-white/10'\n            }`}\n        >\n          Limit\n        </button>\n      </div>\n\n      {/* Trade Inputs */}\n      <div className=\"space-y-3\">\n        {tradeType === 'buy' ? (\n          <>\n            {/* Amount Input */}\n            <div>\n              <label className=\"block text-xs text-white/60 mb-1\">Amount (USD)</label>\n              <input\n                type=\"number\"\n                value={amount}\n                onChange={(e) => setAmount(e.target.value)}\n                className=\"w-full bg-black/40 border border-white/10 rounded-md px-3 py-2 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-green-500 transition\"\n                placeholder=\"0.00\"\n              />\n              {/* Quick Amount Buttons */}\n              <div className=\"flex gap-2 mt-2\">\n                {quickAmounts.map((value) => (\n                  <button\n                    key={value}\n                    onClick={() => handleQuickAmount(value)}\n                    className=\"flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors text-xs\"\n                  >\n                    ${value}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </>\n        ) : (\n          <>\n            {/* Sell Percentage */}\n            <div>\n              <label className=\"block text-xs text-white/60 mb-1\">Sell Percentage</label>\n              <div className=\"flex items-center gap-2\">\n                <input\n                  type=\"range\"\n                  min=\"1\"\n                  max=\"100\"\n                  value={sellPercent}\n                  onChange={(e) => setSellPercent(parseInt(e.target.value))}\n                  className=\"flex-1 accent-red-500\"\n                />\n                <span className=\"text-white font-mono text-sm w-12\">{sellPercent}%</span>\n              </div>\n              {/* Quick Percentage Buttons */}\n              <div className=\"flex gap-2 mt-2\">\n                {quickPercentages.map((value) => (\n                  <button\n                    key={value}\n                    onClick={() => handleQuickPercentage(value)}\n                    className=\"flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors text-xs\"\n                  >\n                    {value}%\n                  </button>\n                ))}\n              </div>\n            </div>\n          </>\n        )}\n\n        {/* Limit Price (only for limit orders) */}\n        <AnimatePresence>\n          {orderType === 'limit' && (\n            <motion.div\n              initial={{ height: 0, opacity: 0 }}\n              animate={{ height: 'auto', opacity: 1 }}\n              exit={{ height: 0, opacity: 0 }}\n              transition={{ duration: 0.2 }}\n            >\n              <label className=\"block text-xs text-white/60 mb-1\">Limit Price</label>\n              <input\n                type=\"number\"\n                value={limitPrice}\n                onChange={(e) => setLimitPrice(e.target.value)}\n                className=\"w-full bg-black/40 border border-white/10 rounded-md px-3 py-2 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500 transition\"\n                placeholder=\"0.00\"\n                step=\"0.000001\"\n              />\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {/* Slippage Setting */}\n        <div>\n          <label className=\"block text-xs text-white/60 mb-1\">Slippage Tolerance</label>\n          <div className=\"flex items-center gap-2\">\n            <input\n              type=\"range\"\n              min=\"0.1\"\n              max=\"5\"\n              step=\"0.1\"\n              value={slippage}\n              onChange={(e) => setSlippage(parseFloat(e.target.value))}\n              className=\"flex-1 accent-blue-500\"\n            />\n            <span className=\"text-white font-mono text-sm w-12\">{slippage}%</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Trade Button */}\n      <motion.button\n        onClick={handleTrade}\n        disabled={isLoading || !amount || parseFloat(amount) <= 0}\n        className={`w-full font-orbitron font-semibold py-3 rounded-md transition-all text-sm ${tradeType === 'buy'\n          ? 'bg-green-600 hover:bg-green-700 text-white'\n          : 'bg-red-600 hover:bg-red-700 text-white'\n          } disabled:opacity-50 disabled:cursor-not-allowed`}\n        whileHover={{ scale: isLoading ? 1 : 1.02 }}\n        whileTap={{ scale: isLoading ? 1 : 0.98 }}\n      >\n        {isLoading ? (\n          <div className=\"flex items-center justify-center gap-2\">\n            <div className=\"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n            Processing...\n          </div>\n        ) : (\n          `${tradeType === 'buy' ? 'Buy' : 'Sell'} ${tokenSymbol}`\n        )}\n      </motion.button>\n\n      {/* Fee Information */}\n      <div className=\"border border-white/10 rounded-md p-3 text-xs text-white/80 bg-black/40\">\n        <div className=\"flex items-center gap-2 mb-2\">\n          <FiInfo size={12} />\n          <span className=\"font-semibold\">Estimated Fees</span>\n        </div>\n        <div className=\"space-y-1\">\n          <div className=\"flex justify-between\">\n            <span>Gas Fee:</span>\n            <span>{fees.gasFee} ETH</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span>Trading Fee:</span>\n            <span>{fees.tradingFee} ETH</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span>Slippage:</span>\n            <span>{fees.slippageFee} ETH</span>\n          </div>\n          <div className=\"flex justify-between border-t border-white/10 pt-1 font-semibold\">\n            <span>Total:</span>\n            <span>{fees.totalFee} ETH</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAqBe,SAAS,aAAa,EACnC,YAAY,EACZ,cAAc,OAAO,EACrB,YAAY,OAAO,EACnB,eAAe,CAAC,EAChB,UAAU,CAAC,EACO;;IAClB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE;QACnD;QACA;QACA,iBAAiB;IACnB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,UAAU;IACZ;IAEA,sCAAsC;IACtC,MAAM,eAAe;QAAC;QAAI;QAAK;QAAK;KAAK;IAEzC,2CAA2C;IAC3C,MAAM,mBAAmB;QAAC;QAAI;QAAI;QAAI;KAAI;IAE1C,yEAAyE;IACzE,MAAM,eAAe,YAAY,SAAS;IAE1C,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,cAAc,WAAW,eAAe,GAAG;gBAC7C,cAAc,aAAa,QAAQ;YACrC;QACF;iCAAG;QAAC;QAAc;KAAU;IAE5B,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,SAAS,gBAAgB,UAAU,WAAW,UAAU,GAAG;gBAC7D,MAAM;wDAAY;wBAChB,IAAI;4BACF,MAAM,cAA2B;gCAC/B,OAAO;gCACP,QAAQ,WAAW;gCACnB;gCACA,YAAY,cAAc,UAAU,WAAW,cAAc;gCAC7D;4BACF;4BACA,MAAM,UAAU,MAAM,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;4BAC5C,QAAQ;wBACV,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,wBAAwB;wBACxC;oBACF;;gBAEA,MAAM,gBAAgB,WAAW,WAAW;gBAC5C;8CAAO,IAAM,aAAa;;YAC5B;QACF;iCAAG;QAAC;QAAO;QAAc;QAAQ;QAAW;QAAY;KAAS;IAEjE,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS,CAAC,cAAc;YAC3B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,UAAU,WAAW,WAAW,GAAG;YACtC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,cAAc,WAAW,CAAC,CAAC,cAAc,WAAW,eAAe,CAAC,GAAG;YACzE,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QACb,IAAI;YACF,IAAI,cAAc,OAAO;gBACvB,MAAM,cAA2B;oBAC/B,OAAO;oBACP,QAAQ,WAAW;oBACnB;oBACA,YAAY,cAAc,UAAU,WAAW,cAAc;oBAC7D;gBACF;gBAEA,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBACzC,IAAI,OAAO,OAAO,EAAE;oBAClB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,cAAc,WAAW,aAAa,SAAS,cAAc,CAAC;oBACzF,UAAU;gBACZ,OAAO;oBACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO,IAAI;gBAChC;YACF,OAAO;gBACL,MAAM,aAAyB;oBAC7B,OAAO;oBACP,SAAS;oBACT;oBACA,YAAY,cAAc,UAAU,WAAW,cAAc;gBAC/D;gBAEA,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,cAAW,AAAD,EAAE,OAAO;gBACxC,IAAI,OAAO,OAAO,EAAE;oBAClB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,cAAc,WAAW,aAAa,SAAS,cAAc,CAAC;oBAC1F,eAAe;gBACjB,OAAO;oBACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO,IAAI;gBAChC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,UAAU,MAAM,QAAQ;IAC1B;IAEA,MAAM,wBAAwB,CAAC;QAC7B,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAiD;4BACtD;;;;;;;kCAET,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC,iJAAA,CAAA,aAAU;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAKtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,qEAAqE,EAAE,cAAc,QAC7F,4BACA,kEACA;;0CAEJ,6LAAC,iJAAA,CAAA,eAAY;gCAAC,WAAU;gCAAc,MAAM;;;;;;4BAAM;;;;;;;kCAGpD,6LAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,qEAAqE,EAAE,cAAc,SAC7F,0BACA,kEACA;;0CAEJ,6LAAC,iJAAA,CAAA,iBAAc;gCAAC,WAAU;gCAAc,MAAM;;;;;;4BAAM;;;;;;;;;;;;;0BAMxD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,+EAA+E,EAAE,cAAc,WACvG,2BACA,qEACA;kCACL;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,+EAA+E,EAAE,cAAc,UACvG,2BACA,qEACA;kCACL;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;;oBACZ,cAAc,sBACb;kCAEE,cAAA,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAmC;;;;;;8CACpD,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;oCACV,aAAY;;;;;;8CAGd,6LAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,6LAAC;4CAEC,SAAS,IAAM,kBAAkB;4CACjC,WAAU;;gDACX;gDACG;;2CAJG;;;;;;;;;;;;;;;;sDAWf;kCAEE,cAAA,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAmC;;;;;;8CACpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;4CACvD,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;;gDAAqC;gDAAY;;;;;;;;;;;;;8CAGnE,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,sBACrB,6LAAC;4CAEC,SAAS,IAAM,sBAAsB;4CACrC,WAAU;;gDAET;gDAAM;;2CAJF;;;;;;;;;;;;;;;;;kCAajB,6LAAC,4LAAA,CAAA,kBAAe;kCACb,cAAc,yBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,QAAQ;gCAAG,SAAS;4BAAE;4BACjC,SAAS;gCAAE,QAAQ;gCAAQ,SAAS;4BAAE;4BACtC,MAAM;gCAAE,QAAQ;gCAAG,SAAS;4BAAE;4BAC9B,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,6LAAC;oCAAM,WAAU;8CAAmC;;;;;;8CACpD,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;oCACV,aAAY;oCACZ,MAAK;;;;;;;;;;;;;;;;;kCAOb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAmC;;;;;;0CACpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,WAAW,EAAE,MAAM,CAAC,KAAK;wCACtD,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;;4CAAqC;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;0BAMpE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,UAAU,aAAa,CAAC,UAAU,WAAW,WAAW;gBACxD,WAAW,CAAC,0EAA0E,EAAE,cAAc,QAClG,+CACA,yCACD,gDAAgD,CAAC;gBACpD,YAAY;oBAAE,OAAO,YAAY,IAAI;gBAAK;gBAC1C,UAAU;oBAAE,OAAO,YAAY,IAAI;gBAAK;0BAEvC,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;wBAAkF;;;;;;2BAInG,GAAG,cAAc,QAAQ,QAAQ,OAAO,CAAC,EAAE,aAAa;;;;;;0BAK5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iJAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;0CACd,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;;kCAElC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;;4CAAM,KAAK,MAAM;4CAAC;;;;;;;;;;;;;0CAErB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;;4CAAM,KAAK,UAAU;4CAAC;;;;;;;;;;;;;0CAEzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;;4CAAM,KAAK,WAAW;4CAAC;;;;;;;;;;;;;0CAE1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;;4CAAM,KAAK,QAAQ;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjC;GAnVwB;;QAOJ,kIAAA,CAAA,UAAO;QACiB,6HAAA,CAAA,aAAU;;;KAR9B", "debugId": null}}, {"offset": {"line": 2887, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Trade/OrderManagement/index.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Fi<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Che<PERSON>, FiAlertCircle, FiRefreshCw } from 'react-icons/fi';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getOpenOrders, getOrderHistory, cancelOrder, type Order } from '@/services/bot/trading';\nimport { toast } from 'react-toastify';\n\ntype TabType = 'open' | 'history';\n\ninterface OrderManagementProps {\n  className?: string;\n}\n\nexport default function OrderManagement({ className = '' }: OrderManagementProps) {\n  const { token } = useAuth();\n  const [activeTab, setActiveTab] = useState<TabType>('open');\n  const [openOrders, setOpenOrders] = useState<Order[]>([]);\n  const [orderHistory, setOrderHistory] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [cancelling, setCancelling] = useState<string | null>(null);\n\n  // Fetch orders\n  const fetchOrders = async () => {\n    if (!token) return;\n    \n    setLoading(true);\n    try {\n      const [open, history] = await Promise.all([\n        getOpenOrders(token),\n        getOrderHistory(token, 20, 0)\n      ]);\n      \n      setOpenOrders(open);\n      setOrderHistory(history.orders);\n    } catch (error) {\n      console.error('Error fetching orders:', error);\n      toast.error('Failed to fetch orders');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initial fetch\n  useEffect(() => {\n    fetchOrders();\n  }, [token]);\n\n  // Auto-refresh every 30 seconds\n  useEffect(() => {\n    const interval = setInterval(fetchOrders, 30000);\n    return () => clearInterval(interval);\n  }, [token]);\n\n  const handleCancelOrder = async (orderId: string) => {\n    if (!token) return;\n    \n    setCancelling(orderId);\n    try {\n      const result = await cancelOrder(token, orderId);\n      if (result.success) {\n        toast.success('Order cancelled successfully');\n        fetchOrders(); // Refresh orders\n      } else {\n        toast.error(result.message || 'Failed to cancel order');\n      }\n    } catch (error) {\n      console.error('Error cancelling order:', error);\n      toast.error('Failed to cancel order');\n    } finally {\n      setCancelling(null);\n    }\n  };\n\n  const getStatusIcon = (status: Order['status']) => {\n    switch (status) {\n      case 'pending':\n        return <FiClock className=\"text-yellow-400\" size={14} />;\n      case 'filled':\n        return <FiCheck className=\"text-green-400\" size={14} />;\n      case 'cancelled':\n        return <FiX className=\"text-gray-400\" size={14} />;\n      case 'failed':\n        return <FiAlertCircle className=\"text-red-400\" size={14} />;\n      default:\n        return <FiClock className=\"text-yellow-400\" size={14} />;\n    }\n  };\n\n  const getStatusColor = (status: Order['status']) => {\n    switch (status) {\n      case 'pending':\n        return 'text-yellow-400';\n      case 'filled':\n        return 'text-green-400';\n      case 'cancelled':\n        return 'text-gray-400';\n      case 'failed':\n        return 'text-red-400';\n      default:\n        return 'text-yellow-400';\n    }\n  };\n\n  const formatDate = (timestamp: number) => {\n    return new Date(timestamp).toLocaleString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const formatPrice = (price: number | undefined) => {\n    if (!price) return '-';\n    return price < 0.01 ? price.toExponential(3) : price.toFixed(6);\n  };\n\n  const OrderRow = ({ order }: { order: Order }) => (\n    <motion.tr\n      initial={{ opacity: 0, y: 10 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -10 }}\n      className=\"hover:bg-white/5 transition-colors\"\n    >\n      <td className=\"px-3 py-2\">\n        <div className=\"flex items-center gap-2\">\n          {getStatusIcon(order.status)}\n          <span className={`text-xs font-medium ${getStatusColor(order.status)}`}>\n            {order.status.toUpperCase()}\n          </span>\n        </div>\n      </td>\n      <td className=\"px-3 py-2\">\n        <div className=\"flex items-center gap-1\">\n          <span className={`text-xs font-semibold ${\n            order.type === 'buy' ? 'text-green-400' : 'text-red-400'\n          }`}>\n            {order.type.toUpperCase()}\n          </span>\n          <span className=\"text-xs text-white/60\">\n            {order.orderType.toUpperCase()}\n          </span>\n        </div>\n      </td>\n      <td className=\"px-3 py-2\">\n        <div>\n          <div className=\"text-xs font-medium text-white\">\n            {order.tokenSymbol || order.token.slice(0, 8)}\n          </div>\n          <div className=\"text-xs text-white/60\">\n            {order.tokenName || 'Unknown Token'}\n          </div>\n        </div>\n      </td>\n      <td className=\"px-3 py-2 text-xs text-white font-mono\">\n        {order.amount.toFixed(4)}\n      </td>\n      <td className=\"px-3 py-2 text-xs text-white font-mono\">\n        {formatPrice(order.price)}\n      </td>\n      <td className=\"px-3 py-2 text-xs text-white/60\">\n        {formatDate(order.timestamp)}\n      </td>\n      {activeTab === 'open' && order.status === 'pending' && (\n        <td className=\"px-3 py-2\">\n          <button\n            onClick={() => handleCancelOrder(order.id)}\n            disabled={cancelling === order.id}\n            className=\"text-red-400 hover:text-red-300 transition-colors disabled:opacity-50\"\n          >\n            {cancelling === order.id ? (\n              <div className=\"w-3 h-3 border border-red-400 border-t-transparent rounded-full animate-spin\"></div>\n            ) : (\n              <FiX size={14} />\n            )}\n          </button>\n        </td>\n      )}\n    </motion.tr>\n  );\n\n  return (\n    <div className={`bg-black/30 border border-white/10 rounded-md ${className}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-white/10\">\n        <div className=\"flex gap-4\">\n          <button\n            onClick={() => setActiveTab('open')}\n            className={`text-sm font-medium transition-colors ${\n              activeTab === 'open'\n                ? 'text-white border-b-2 border-green-500 pb-1'\n                : 'text-white/60 hover:text-white'\n            }`}\n          >\n            Open Orders ({openOrders.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('history')}\n            className={`text-sm font-medium transition-colors ${\n              activeTab === 'history'\n                ? 'text-white border-b-2 border-blue-500 pb-1'\n                : 'text-white/60 hover:text-white'\n            }`}\n          >\n            Order History\n          </button>\n        </div>\n        \n        <button\n          onClick={fetchOrders}\n          disabled={loading}\n          className=\"p-1.5 rounded-lg hover:bg-white/10 text-white/60 hover:text-white transition-colors disabled:opacity-50\"\n        >\n          <FiRefreshCw className={loading ? 'animate-spin' : ''} size={16} />\n        </button>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-4\">\n        {loading ? (\n          <div className=\"flex items-center justify-center py-8\">\n            <div className=\"w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n          </div>\n        ) : (\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full text-left\">\n              <thead>\n                <tr className=\"text-white/60 text-xs\">\n                  <th className=\"px-3 py-2 font-medium\">Status</th>\n                  <th className=\"px-3 py-2 font-medium\">Type</th>\n                  <th className=\"px-3 py-2 font-medium\">Token</th>\n                  <th className=\"px-3 py-2 font-medium\">Amount</th>\n                  <th className=\"px-3 py-2 font-medium\">Price</th>\n                  <th className=\"px-3 py-2 font-medium\">Time</th>\n                  {activeTab === 'open' && <th className=\"px-3 py-2 font-medium\">Action</th>}\n                </tr>\n              </thead>\n              <tbody>\n                <AnimatePresence>\n                  {activeTab === 'open' ? (\n                    openOrders.length > 0 ? (\n                      openOrders.map((order) => (\n                        <OrderRow key={order.id} order={order} />\n                      ))\n                    ) : (\n                      <tr>\n                        <td colSpan={7} className=\"px-3 py-8 text-center text-white/60\">\n                          No open orders\n                        </td>\n                      </tr>\n                    )\n                  ) : (\n                    orderHistory.length > 0 ? (\n                      orderHistory.map((order) => (\n                        <OrderRow key={order.id} order={order} />\n                      ))\n                    ) : (\n                      <tr>\n                        <td colSpan={6} className=\"px-3 py-8 text-center text-white/60\">\n                          No order history\n                        </td>\n                      </tr>\n                    )\n                  )}\n                </AnimatePresence>\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAee,SAAS,gBAAgB,EAAE,YAAY,EAAE,EAAwB;;IAC9E,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,eAAe;IACf,MAAM,cAAc;QAClB,IAAI,CAAC,OAAO;QAEZ,WAAW;QACX,IAAI;YACF,MAAM,CAAC,MAAM,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACxC,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD,EAAE;gBACd,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,IAAI;aAC5B;YAED,cAAc;YACd,gBAAgB,QAAQ,MAAM;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG;QAAC;KAAM;IAEV,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,WAAW,YAAY,aAAa;YAC1C;6CAAO,IAAM,cAAc;;QAC7B;oCAAG;QAAC;KAAM;IAEV,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,OAAO;QAEZ,cAAc;QACd,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,cAAW,AAAD,EAAE,OAAO;YACxC,IAAI,OAAO,OAAO,EAAE;gBAClB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,eAAe,iBAAiB;YAClC,OAAO;gBACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,UAAO;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YACpD,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,UAAO;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACnD,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,MAAG;oBAAC,WAAU;oBAAgB,MAAM;;;;;;YAC9C,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,gBAAa;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACvD;gBACE,qBAAO,6LAAC,iJAAA,CAAA,UAAO;oBAAC,WAAU;oBAAkB,MAAM;;;;;;QACtD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,WAAW,cAAc,CAAC,SAAS;YACjD,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,OAAO,OAAO;QACnB,OAAO,QAAQ,OAAO,MAAM,aAAa,CAAC,KAAK,MAAM,OAAO,CAAC;IAC/D;IAEA,MAAM,WAAW,CAAC,EAAE,KAAK,EAAoB,iBAC3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;YACR,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC3B,WAAU;;8BAEV,6LAAC;oBAAG,WAAU;8BACZ,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,MAAM,MAAM;0CAC3B,6LAAC;gCAAK,WAAW,CAAC,oBAAoB,EAAE,eAAe,MAAM,MAAM,GAAG;0CACnE,MAAM,MAAM,CAAC,WAAW;;;;;;;;;;;;;;;;;8BAI/B,6LAAC;oBAAG,WAAU;8BACZ,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAW,CAAC,sBAAsB,EACtC,MAAM,IAAI,KAAK,QAAQ,mBAAmB,gBAC1C;0CACC,MAAM,IAAI,CAAC,WAAW;;;;;;0CAEzB,6LAAC;gCAAK,WAAU;0CACb,MAAM,SAAS,CAAC,WAAW;;;;;;;;;;;;;;;;;8BAIlC,6LAAC;oBAAG,WAAU;8BACZ,cAAA,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;0CACZ,MAAM,WAAW,IAAI,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG;;;;;;0CAE7C,6LAAC;gCAAI,WAAU;0CACZ,MAAM,SAAS,IAAI;;;;;;;;;;;;;;;;;8BAI1B,6LAAC;oBAAG,WAAU;8BACX,MAAM,MAAM,CAAC,OAAO,CAAC;;;;;;8BAExB,6LAAC;oBAAG,WAAU;8BACX,YAAY,MAAM,KAAK;;;;;;8BAE1B,6LAAC;oBAAG,WAAU;8BACX,WAAW,MAAM,SAAS;;;;;;gBAE5B,cAAc,UAAU,MAAM,MAAM,KAAK,2BACxC,6LAAC;oBAAG,WAAU;8BACZ,cAAA,6LAAC;wBACC,SAAS,IAAM,kBAAkB,MAAM,EAAE;wBACzC,UAAU,eAAe,MAAM,EAAE;wBACjC,WAAU;kCAET,eAAe,MAAM,EAAE,iBACtB,6LAAC;4BAAI,WAAU;;;;;iDAEf,6LAAC,iJAAA,CAAA,MAAG;4BAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;IAQvB,qBACE,6LAAC;QAAI,WAAW,CAAC,8CAA8C,EAAE,WAAW;;0BAE1E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,sCAAsC,EAChD,cAAc,SACV,gDACA,kCACJ;;oCACH;oCACe,WAAW,MAAM;oCAAC;;;;;;;0CAElC,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,sCAAsC,EAChD,cAAc,YACV,+CACA,kCACJ;0CACH;;;;;;;;;;;;kCAKH,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAEV,cAAA,6LAAC,iJAAA,CAAA,cAAW;4BAAC,WAAW,UAAU,iBAAiB;4BAAI,MAAM;;;;;;;;;;;;;;;;;0BAKjE,6LAAC;gBAAI,WAAU;0BACZ,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;yCAGjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;0CACC,cAAA,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;wCACrC,cAAc,wBAAU,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAGnE,6LAAC;0CACC,cAAA,6LAAC,4LAAA,CAAA,kBAAe;8CACb,cAAc,SACb,WAAW,MAAM,GAAG,IAClB,WAAW,GAAG,CAAC,CAAC,sBACd,6LAAC;4CAAwB,OAAO;2CAAjB,MAAM,EAAE;;;;kEAGzB,6LAAC;kDACC,cAAA,6LAAC;4CAAG,SAAS;4CAAG,WAAU;sDAAsC;;;;;;;;;;+CAMpE,aAAa,MAAM,GAAG,IACpB,aAAa,GAAG,CAAC,CAAC,sBAChB,6LAAC;4CAAwB,OAAO;2CAAjB,MAAM,EAAE;;;;kEAGzB,6LAAC;kDACC,cAAA,6LAAC;4CAAG,SAAS;4CAAG,WAAU;sDAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcxF;GAnQwB;;QACJ,kIAAA,CAAA,UAAO;;;KADH", "debugId": null}}, {"offset": {"line": 3451, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/app/terminal/trade/%5Bchain%5D/%5Baddress%5D/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useParams } from \"next/navigation\"\r\nimport Trending from \"@/components/Terminal/Trade/Trending\";\r\nimport RecentlyViewed from \"@/components/Terminal/Trade/Trending/RecentlyViewed\";\r\nimport { useWeb3 } from \"@/contexts/Web3Context\";\r\nimport { useEffect } from \"react\";\r\nimport OpenPositions from \"@/components/Terminal/Trade/OpenPosition\";\r\nimport TokenPairData from \"@/components/Terminal/Trade/TokenPairData\";\r\nimport TradingPanel from \"@/components/Terminal/Trade/TradingPanel\";\r\nimport OrderManagement from \"@/components/Terminal/Trade/OrderManagement\";\r\n\r\nexport default function TradePage() {\r\n    const { chain, address } = useParams();\r\n    const { addRecentlyViewed } = useWeb3();\r\n\r\n    useEffect(() => {\r\n        // Only add if both are present and address is 0x-prefixed\r\n        if (typeof address === 'string' && typeof chain === 'string' && address.startsWith('0x')) {\r\n            addRecentlyViewed(address as `0x${string}`, chain);\r\n        }\r\n    }, [address, chain, addRecentlyViewed]);\r\n\r\n    useEffect(() => {\r\n        document.title = \"📊 Live Trading Dashboard - Real-Time Crypto Trading | Anubis Terminal\";\r\n    }, []);\r\n\r\n    return (\r\n        <section className=\"overflow-hidden grid grid-cols-12 h-[calc(100vh-80px)]\">\r\n            <div className=\"col-span-2 border-r border-white/10 w-full overflow-x-hidden h-full grid grid-rows-3\">\r\n                <Trending />\r\n                <RecentlyViewed />\r\n                <OpenPositions />\r\n            </div>\r\n            <div className=\"col-span-6 h-full flex flex-col\">\r\n                {/* Main content area */}\r\n                <div className=\"flex-1\">\r\n                    <TokenPairData />\r\n                </div>\r\n                {/* Order Management */}\r\n                <div className=\"h-80 border-t border-white/10\">\r\n                    <OrderManagement className=\"h-full\" />\r\n                </div>\r\n            </div>\r\n            <div className=\"col-span-4 border-l border-white/10 h-full\">\r\n                {/* Professional Trading Panel */}\r\n                <TradingPanel\r\n                    tokenAddress={address as string}\r\n                    tokenSymbol=\"TOKEN\"\r\n                    tokenName=\"Token Name\"\r\n                    currentPrice={0}\r\n                    chainId={1}\r\n                />\r\n            </div>\r\n        </section>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;;AAWe,SAAS;;IACpB,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACnC,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,0DAA0D;YAC1D,IAAI,OAAO,YAAY,YAAY,OAAO,UAAU,YAAY,QAAQ,UAAU,CAAC,OAAO;gBACtF,kBAAkB,SAA0B;YAChD;QACJ;8BAAG;QAAC;QAAS;QAAO;KAAkB;IAEtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,SAAS,KAAK,GAAG;QACrB;8BAAG,EAAE;IAEL,qBACI,6LAAC;QAAQ,WAAU;;0BACf,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,+JAAA,CAAA,UAAQ;;;;;kCACT,6LAAC,iLAAA,CAAA,UAAc;;;;;kCACf,6LAAC,mKAAA,CAAA,UAAa;;;;;;;;;;;0BAElB,6LAAC;gBAAI,WAAU;;kCAEX,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC,oKAAA,CAAA,UAAa;;;;;;;;;;kCAGlB,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC,sKAAA,CAAA,UAAe;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAGnC,6LAAC;gBAAI,WAAU;0BAEX,cAAA,6LAAC,mKAAA,CAAA,UAAY;oBACT,cAAc;oBACd,aAAY;oBACZ,WAAU;oBACV,cAAc;oBACd,SAAS;;;;;;;;;;;;;;;;;AAK7B;GA5CwB;;QACO,qIAAA,CAAA,YAAS;QACN,kIAAA,CAAA,UAAO;;;KAFjB", "debugId": null}}]}