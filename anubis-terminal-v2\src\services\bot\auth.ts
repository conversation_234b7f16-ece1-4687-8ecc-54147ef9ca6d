import { api } from './index';

export interface TwoFactorResponse {
  message: string;
  success: boolean;
}

export interface VerifyTwoFactorParams {
  code: string;
}

/**
 * Send 2FA code to user's Telegram
 */
export const send2FACode = async (token: string): Promise<TwoFactorResponse> => {
  try {
    const response = await api.post<TwoFactorResponse>(
      '/bot/user/2fa/send',
      {},
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error sending 2FA code:', error);
    throw new Error('Failed to send 2FA code');
  }
};

/**
 * Verify 2FA code
 */
export const verify2FACode = async (
  token: string,
  code: string
): Promise<TwoFactorResponse> => {
  try {
    const response = await api.post<TwoFactorResponse>(
      '/bot/user/2fa/verify',
      { code },
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error verifying 2FA code:', error);
    throw new Error('Failed to verify 2FA code');
  }
};
