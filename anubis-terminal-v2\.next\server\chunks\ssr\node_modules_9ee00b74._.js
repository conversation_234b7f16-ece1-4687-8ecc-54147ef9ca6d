module.exports = {

"[project]/node_modules/viem/_esm/utils/ccip.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_viem__esm_3903dffb._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/viem/_esm/utils/ccip.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@noble/curves/esm/secp256k1.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@noble/curves/esm/secp256k1.js [app-ssr] (ecmascript)");
    });
});
}}),

};