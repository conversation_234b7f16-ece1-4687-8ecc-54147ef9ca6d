{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_35b02a57.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_35b02a57-module__BOCH1a__className\",\n  \"variable\": \"geist_35b02a57-module__BOCH1a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_35b02a57.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_87bc4ef9.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_87bc4ef9-module__963okG__className\",\n  \"variable\": \"geist_mono_87bc4ef9-module__963okG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_87bc4ef9.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,6JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,6JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,6JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/orbitron_94041d18.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"orbitron_94041d18-module__LQRA4G__className\",\n  \"variable\": \"orbitron_94041d18-module__LQRA4G__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/orbitron_94041d18.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Orbitron%22,%22arguments%22:[{%22variable%22:%22--font-orbitron%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22orbitron%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Orbitron', 'Orbitron Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,2JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,2JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,2JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/space_grotesk_b1377d82.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"space_grotesk_b1377d82-module__c9cn1G__className\",\n  \"variable\": \"space_grotesk_b1377d82-module__c9cn1G__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/space_grotesk_b1377d82.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Space_Grotesk%22,%22arguments%22:[{%22variable%22:%22--font-space-grotesk%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22spaceGrotesk%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Space Grotesk', 'Space Grotesk Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/utils/data.ts"], "sourcesContent": ["export interface GlobalNavLinks {\r\n    title: string;\r\n    url: string;\r\n}\r\n\r\nexport const globalNavLinks = [\r\n    {\r\n        title: \"Features\",\r\n        url: \"#features\"\r\n    },\r\n    {\r\n        title: \"Anubis Teams\",\r\n        url: \"/anubis-teams\"\r\n    },\r\n    {\r\n        title: \"Documentation\",\r\n        url: \"/documentation\"\r\n    },\r\n    {\r\n        title: \"Browser Extension\",\r\n        url: \"#browser-extension\"\r\n    },\r\n    {\r\n        title: \"Tutorials\",\r\n        url: \"/tutorials\"\r\n    }\r\n]\r\n\r\n// export const socials = [\r\n//     {\r\n//         url:\"https://t.me/AnubisBot\"\r\n//     }\r\n// ]`\r\n\r\n\r\nexport const terminalNavLinks = [\r\n    {\r\n        title: \"Trending\",\r\n        url: \"/terminal/trending\"\r\n    },\r\n    {\r\n        title: \"Gainers\",\r\n        url: \"/terminal/gainers\"\r\n    },\r\n    {\r\n        title: \"New\",\r\n        url: \"/terminal/new\"\r\n    },\r\n    {\r\n        title: \"portfolio\",\r\n        url: \"/terminal/portfolio\"\r\n    },\r\n    {\r\n        title: \"Wallets\",\r\n        url: \"/terminal/wallets\"\r\n    },\r\n    {\r\n        title: \"Sniping\",\r\n        url: \"/terminal/sniping\"\r\n    }\r\n]\r\n\r\nexport const tokenData = [\r\n    {\r\n        name: \"Bitcoin\",\r\n        symbol: \"BTC\",\r\n        price: \"$20,000\",\r\n        change: \"+2.5%\",\r\n        volume: \"$100,000,000\",\r\n        marketCap: \"$1,000,000,000\",\r\n        logo: \"/logo/icon.png\"\r\n    }\r\n]\r\n\r\nexport const chains = [\r\n    {\r\n        chain: \"Avax\",\r\n        logo: \"/chains/avax.svg\",\r\n        slug: \"avalanche\",\r\n        scanUrl: \"https://snowtrace.io\",\r\n        chainId: 43114,\r\n        symbol: \"AVAX\"\r\n    },\r\n    {\r\n        chain: \"Base\",\r\n        logo: \"/chains/base.svg\",\r\n        slug: \"base\",\r\n        scanUrl: \"https://basescan.org\",\r\n        chainId: 8453,\r\n        symbol: \"ETH\"\r\n    },\r\n    {\r\n        chain: \"BeraChain\",\r\n        logo: \"/chains/berachain.svg\",\r\n        slug: \"berachain\",\r\n        scanUrl: \"https://artio.beratrail.io\",\r\n        chainId: 80094,\r\n        symbol: \"BERA\"\r\n    },\r\n    {\r\n        chain: \"Sonic\",\r\n        logo: \"/chains/sonic.svg\",\r\n        slug: \"sonic\",\r\n        scanUrl: \"https://explorer.sonic.ooo\",\r\n        chainId: 146,\r\n        symbol: \"S\"\r\n    },\r\n    {\r\n        chain: \"Unichain\",\r\n        logo: \"/chains/unichain.svg\",\r\n        slug: \"unichain\",\r\n        scanUrl: \"https://unichainscan.com\",\r\n        chainId: 130,\r\n        symbol: \"U ETH\"\r\n    },\r\n]\r\n\r\nexport function calculateFDV(totalSupplyStr: string, priceStr: string): number {\r\n    const totalSupply = parseFloat(totalSupplyStr);\r\n    const priceUSD = parseFloat(priceStr);\r\n\r\n    if (isNaN(totalSupply) || isNaN(priceUSD)) {\r\n        throw new Error(\"Invalid number format in input\");\r\n    }\r\n\r\n    const fdv = totalSupply * priceUSD;\r\n    return parseFloat(fdv.toFixed(2)); // rounded to 2 decimals (USD convention)\r\n}\r\n\r\nexport function getTimeAgo(timestamp: number): string {\r\n    const now = Date.now() / 1000; // Current time in seconds\r\n    const secondsAgo = now - timestamp;\r\n\r\n    if (secondsAgo < 60) {\r\n        return \"<1M\";\r\n    }\r\n\r\n    const minutes = Math.floor(secondsAgo / 60);\r\n    if (minutes < 60) {\r\n        return `${minutes}M`;\r\n    }\r\n\r\n    const hours = Math.floor(minutes / 60);\r\n    if (hours < 24) {\r\n        return `${hours}H`;\r\n    }\r\n\r\n    const days = Math.floor(hours / 24);\r\n    return `${days}D`;\r\n}\r\n\r\ntype Activity = {\r\n    volume1: string;\r\n    volume4: string;\r\n    volume12: string;\r\n    volume24: string;\r\n};\r\n\r\nexport function getVolumeByTimeOption(activity: Activity, timeOption: string): number {\r\n    const mapping: Record<string, keyof Activity> = {\r\n        \"1h\": \"volume1\",\r\n        \"4h\": \"volume4\",\r\n        \"12h\": \"volume12\",\r\n        \"24h\": \"volume24\"\r\n    };\r\n\r\n    const key = mapping[timeOption];\r\n    if (!key) {\r\n        throw new Error(`Invalid time option: ${timeOption}`);\r\n    }\r\n\r\n    const volume = parseFloat(activity[key]);\r\n    return isNaN(volume) ? 0 : volume;\r\n}\r\n\r\ntype PriceChange = {\r\n    change1: string;\r\n    change4: string;\r\n    change12: string;\r\n    change24: string;\r\n};\r\n\r\nexport function getPriceChangeByTimeOption(activity: PriceChange, timeOption: string): number {\r\n    const mapping: Record<string, keyof PriceChange> = {\r\n        \"1h\": \"change1\",\r\n        \"4h\": \"change4\",\r\n        \"12h\": \"change12\",\r\n        \"24h\": \"change24\"\r\n    };\r\n\r\n    const key = mapping[timeOption];\r\n    if (!key) {\r\n        throw new Error(`Invalid time option: ${timeOption}`);\r\n    }\r\n\r\n    const change = parseFloat(activity[key]);\r\n    return isNaN(change) ? 0 : change;\r\n}\r\n\r\n\r\ntype UniqueBuys = {\r\n    uniqueBuys1: number;\r\n    uniqueBuys4: number;\r\n    uniqueBuys12: number;\r\n    uniqueBuys24: number;\r\n};\r\n\r\nexport function getUniqueBuysByTimeOption(activity: UniqueBuys, timeOption: string): number {\r\n    const mapping: Record<string, keyof UniqueBuys> = {\r\n        \"1h\": \"uniqueBuys1\",\r\n        \"4h\": \"uniqueBuys4\",\r\n        \"12h\": \"uniqueBuys12\",\r\n        \"24h\": \"uniqueBuys24\"\r\n    };\r\n\r\n    const key = mapping[timeOption];\r\n    if (!key) {\r\n        throw new Error(`Invalid time option: ${timeOption}`);\r\n    }\r\n\r\n    const value = activity[key];\r\n    return typeof value === \"number\" ? value : 0;\r\n}\r\n\r\ntype UniqueSells = {\r\n    uniqueSells1: number;\r\n    uniqueSells4: number;\r\n    uniqueSells12: number;\r\n    uniqueSells24: number;\r\n};\r\n\r\nexport function getUniqueSellsByTimeOption(activity: UniqueSells, timeOption: string): number {\r\n    const mapping: Record<string, keyof UniqueSells> = {\r\n        \"1h\": \"uniqueSells1\",\r\n        \"4h\": \"uniqueSells4\",\r\n        \"12h\": \"uniqueSells12\",\r\n        \"24h\": \"uniqueSells24\"\r\n    };\r\n\r\n    const key = mapping[timeOption];\r\n    if (!key) {\r\n        throw new Error(`Invalid time option: ${timeOption}`);\r\n    }\r\n\r\n    const value = activity[key];\r\n    return typeof value === \"number\" ? value : 0;\r\n}\r\n\r\n\r\nexport const baseURL: string = process.env.NODE_ENV === \"production\" ? \"https://anubis-terminal-v2.vercel.app\" : \"http://localhost:3000\";\r\n\r\nexport function getShareLink(chain: string, address: string): string {\r\n    return `${baseURL}/terminal/trade/${chain}/${address}`;\r\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;AAyP+B;AApPxB,MAAM,iBAAiB;IAC1B;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;CACH;AASM,MAAM,mBAAmB;IAC5B;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;CACH;AAEM,MAAM,YAAY;IACrB;QACI,MAAM;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,MAAM;IACV;CACH;AAEM,MAAM,SAAS;IAClB;QACI,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS;QACT,SAAS;QACT,QAAQ;IACZ;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS;QACT,SAAS;QACT,QAAQ;IACZ;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS;QACT,SAAS;QACT,QAAQ;IACZ;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS;QACT,SAAS;QACT,QAAQ;IACZ;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS;QACT,SAAS;QACT,QAAQ;IACZ;CACH;AAEM,SAAS,aAAa,cAAsB,EAAE,QAAgB;IACjE,MAAM,cAAc,WAAW;IAC/B,MAAM,WAAW,WAAW;IAE5B,IAAI,MAAM,gBAAgB,MAAM,WAAW;QACvC,MAAM,IAAI,MAAM;IACpB;IAEA,MAAM,MAAM,cAAc;IAC1B,OAAO,WAAW,IAAI,OAAO,CAAC,KAAK,yCAAyC;AAChF;AAEO,SAAS,WAAW,SAAiB;IACxC,MAAM,MAAM,KAAK,GAAG,KAAK,MAAM,0BAA0B;IACzD,MAAM,aAAa,MAAM;IAEzB,IAAI,aAAa,IAAI;QACjB,OAAO;IACX;IAEA,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa;IACxC,IAAI,UAAU,IAAI;QACd,OAAO,GAAG,QAAQ,CAAC,CAAC;IACxB;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,IAAI,QAAQ,IAAI;QACZ,OAAO,GAAG,MAAM,CAAC,CAAC;IACtB;IAEA,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ;IAChC,OAAO,GAAG,KAAK,CAAC,CAAC;AACrB;AASO,SAAS,sBAAsB,QAAkB,EAAE,UAAkB;IACxE,MAAM,UAA0C;QAC5C,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;IACX;IAEA,MAAM,MAAM,OAAO,CAAC,WAAW;IAC/B,IAAI,CAAC,KAAK;QACN,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,YAAY;IACxD;IAEA,MAAM,SAAS,WAAW,QAAQ,CAAC,IAAI;IACvC,OAAO,MAAM,UAAU,IAAI;AAC/B;AASO,SAAS,2BAA2B,QAAqB,EAAE,UAAkB;IAChF,MAAM,UAA6C;QAC/C,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;IACX;IAEA,MAAM,MAAM,OAAO,CAAC,WAAW;IAC/B,IAAI,CAAC,KAAK;QACN,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,YAAY;IACxD;IAEA,MAAM,SAAS,WAAW,QAAQ,CAAC,IAAI;IACvC,OAAO,MAAM,UAAU,IAAI;AAC/B;AAUO,SAAS,0BAA0B,QAAoB,EAAE,UAAkB;IAC9E,MAAM,UAA4C;QAC9C,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;IACX;IAEA,MAAM,MAAM,OAAO,CAAC,WAAW;IAC/B,IAAI,CAAC,KAAK;QACN,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,YAAY;IACxD;IAEA,MAAM,QAAQ,QAAQ,CAAC,IAAI;IAC3B,OAAO,OAAO,UAAU,WAAW,QAAQ;AAC/C;AASO,SAAS,2BAA2B,QAAqB,EAAE,UAAkB;IAChF,MAAM,UAA6C;QAC/C,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;IACX;IAEA,MAAM,MAAM,OAAO,CAAC,WAAW;IAC/B,IAAI,CAAC,KAAK;QACN,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,YAAY;IACxD;IAEA,MAAM,QAAQ,QAAQ,CAAC,IAAI;IAC3B,OAAO,OAAO,UAAU,WAAW,QAAQ;AAC/C;AAGO,MAAM,UAAkB,6EAAkF;AAE1G,SAAS,aAAa,KAAa,EAAE,OAAe;IACvD,OAAO,GAAG,QAAQ,gBAAgB,EAAE,MAAM,CAAC,EAAE,SAAS;AAC1D", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/services/bot/trading.ts"], "sourcesContent": ["import { api } from './index';\n\n// Trading Types\nexport interface TradeParams {\n  token: string;\n  amount: number;\n  orderType?: 'market' | 'limit';\n  limitPrice?: number;\n  slippage?: number;\n}\n\nexport interface SellParams {\n  token: string;\n  percent: number;\n  orderType?: 'market' | 'limit';\n  limitPrice?: number;\n}\n\nexport interface Order {\n  id: string;\n  type: 'buy' | 'sell';\n  orderType: 'market' | 'limit';\n  token: string;\n  tokenSymbol?: string;\n  tokenName?: string;\n  amount: number;\n  price?: number;\n  status: 'pending' | 'filled' | 'cancelled' | 'failed';\n  timestamp: number;\n  txHash?: string;\n  chainId: number;\n}\n\nexport interface Position {\n  id: string;\n  token: string;\n  tokenSymbol: string;\n  tokenName: string;\n  amount: string;\n  entryPrice: number;\n  currentPrice: number;\n  pnl: number;\n  pnlPercent: number;\n  timestamp: number;\n  chainId: number;\n}\n\nexport interface TradeResponse {\n  success: boolean;\n  message: string;\n  token?: string;\n  amount?: number;\n  txHash?: string;\n  orderId?: string;\n}\n\n/**\n * Execute a buy trade\n */\nexport const executeTrade = async (\n  token: string,\n  params: TradeParams\n): Promise<TradeResponse> => {\n  try {\n    const response = await api.post('/trade/token', {\n      token: params.token,\n      amount: params.amount,\n      orderType: params.orderType || 'market',\n      limitPrice: params.limitPrice,\n      slippage: params.slippage\n    }, {\n      headers: { Authorization: `Bearer ${token}` },\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error executing trade:', error);\n    throw new Error('Failed to execute trade');\n  }\n};\n\n/**\n * Execute a snipe trade\n */\nexport const executeSnipe = async (\n  token: string,\n  params: TradeParams\n): Promise<TradeResponse> => {\n  try {\n    const response = await api.post('/trade/snipe', {\n      token: params.token,\n      amount: params.amount\n    }, {\n      headers: { Authorization: `Bearer ${token}` },\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error executing snipe:', error);\n    throw new Error('Failed to execute snipe');\n  }\n};\n\n/**\n * Execute a sell trade\n */\nexport const executeSell = async (\n  token: string,\n  params: SellParams\n): Promise<TradeResponse> => {\n  try {\n    const response = await api.post('/trade/sell', {\n      token: params.token,\n      percent: params.percent,\n      orderType: params.orderType || 'market',\n      limitPrice: params.limitPrice\n    }, {\n      headers: { Authorization: `Bearer ${token}` },\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error executing sell:', error);\n    throw new Error('Failed to execute sell');\n  }\n};\n\n/**\n * Get open orders\n */\nexport const getOpenOrders = async (token: string): Promise<Order[]> => {\n  try {\n    const response = await api.get('/trade/orders/open', {\n      headers: { Authorization: `Bearer ${token}` },\n    });\n    return response.data.orders || [];\n  } catch (error) {\n    console.error('Error fetching open orders:', error);\n    // Return empty array if endpoint doesn't exist yet\n    return [];\n  }\n};\n\n/**\n * Get order history\n */\nexport const getOrderHistory = async (\n  token: string,\n  limit: number = 50,\n  offset: number = 0\n): Promise<{\n  orders: Order[];\n  total: number;\n  hasMore: boolean;\n}> => {\n  try {\n    const response = await api.get('/trade/orders/history', {\n      params: { limit, offset },\n      headers: { Authorization: `Bearer ${token}` },\n    });\n    return {\n      orders: response.data.orders || [],\n      total: response.data.total || 0,\n      hasMore: response.data.hasMore || false\n    };\n  } catch (error) {\n    console.error('Error fetching order history:', error);\n    // Return empty result if endpoint doesn't exist yet\n    return {\n      orders: [],\n      total: 0,\n      hasMore: false\n    };\n  }\n};\n\n/**\n * Get current positions\n */\nexport const getPositions = async (token: string): Promise<Position[]> => {\n  try {\n    const response = await api.get('/trade/positions', {\n      headers: { Authorization: `Bearer ${token}` },\n    });\n    return response.data.positions || [];\n  } catch (error) {\n    console.error('Error fetching positions:', error);\n    // Return empty array if endpoint doesn't exist yet\n    return [];\n  }\n};\n\n/**\n * Cancel an open order\n */\nexport const cancelOrder = async (\n  token: string,\n  orderId: string\n): Promise<{ success: boolean; message: string }> => {\n  try {\n    const response = await api.delete(`/trade/orders/${orderId}`, {\n      headers: { Authorization: `Bearer ${token}` },\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error cancelling order:', error);\n    throw new Error('Failed to cancel order');\n  }\n};\n\n/**\n * Get market data for a token\n */\nexport const getMarketData = async (\n  token: string,\n  tokenAddress: string,\n  chainId: number\n): Promise<{\n  price: number;\n  priceChange24h: number;\n  volume24h: number;\n  marketCap: number;\n  liquidity: number;\n}> => {\n  try {\n    const response = await api.get(`/bot/user/token/${tokenAddress}`, {\n      params: { chainId },\n      headers: { Authorization: `Bearer ${token}` },\n    });\n    \n    return {\n      price: parseFloat(response.data.priceUsd || '0'),\n      priceChange24h: parseFloat(response.data.priceChange24h || '0'),\n      volume24h: parseFloat(response.data.volume24h || '0'),\n      marketCap: parseFloat(response.data.fdv || '0'),\n      liquidity: parseFloat(response.data.liquidity || '0')\n    };\n  } catch (error) {\n    console.error('Error fetching market data:', error);\n    throw new Error('Failed to fetch market data');\n  }\n};\n\n/**\n * Get trading fees and gas estimates\n */\nexport const getTradingFees = async (\n  token: string,\n  tradeParams: TradeParams\n): Promise<{\n  gasFee: string;\n  tradingFee: string;\n  slippageFee: string;\n  totalFee: string;\n}> => {\n  try {\n    const response = await api.post('/trade/fees', tradeParams, {\n      headers: { Authorization: `Bearer ${token}` },\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching trading fees:', error);\n    // Return default values if endpoint doesn't exist yet\n    return {\n      gasFee: '0.001',\n      tradingFee: '0.003',\n      slippageFee: '0.001',\n      totalFee: '0.005'\n    };\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAAA;;AA2DO,MAAM,eAAe,OAC1B,OACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,IAAI,CAAC,gBAAgB;YAC9C,OAAO,OAAO,KAAK;YACnB,QAAQ,OAAO,MAAM;YACrB,WAAW,OAAO,SAAS,IAAI;YAC/B,YAAY,OAAO,UAAU;YAC7B,UAAU,OAAO,QAAQ;QAC3B,GAAG;YACD,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,eAAe,OAC1B,OACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,IAAI,CAAC,gBAAgB;YAC9C,OAAO,OAAO,KAAK;YACnB,QAAQ,OAAO,MAAM;QACvB,GAAG;YACD,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,cAAc,OACzB,OACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,IAAI,CAAC,eAAe;YAC7C,OAAO,OAAO,KAAK;YACnB,SAAS,OAAO,OAAO;YACvB,WAAW,OAAO,SAAS,IAAI;YAC/B,YAAY,OAAO,UAAU;QAC/B,GAAG;YACD,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,GAAG,CAAC,sBAAsB;YACnD,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QACA,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,mDAAmD;QACnD,OAAO,EAAE;IACX;AACF;AAKO,MAAM,kBAAkB,OAC7B,OACA,QAAgB,EAAE,EAClB,SAAiB,CAAC;IAMlB,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,GAAG,CAAC,yBAAyB;YACtD,QAAQ;gBAAE;gBAAO;YAAO;YACxB,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QACA,OAAO;YACL,QAAQ,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;YAClC,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI;YAC9B,SAAS,SAAS,IAAI,CAAC,OAAO,IAAI;QACpC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,oDAAoD;QACpD,OAAO;YACL,QAAQ,EAAE;YACV,OAAO;YACP,SAAS;QACX;IACF;AACF;AAKO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,GAAG,CAAC,oBAAoB;YACjD,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QACA,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,EAAE;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,mDAAmD;QACnD,OAAO,EAAE;IACX;AACF;AAKO,MAAM,cAAc,OACzB,OACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,SAAS,EAAE;YAC5D,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,gBAAgB,OAC3B,OACA,cACA;IAQA,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,cAAc,EAAE;YAChE,QAAQ;gBAAE;YAAQ;YAClB,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QAEA,OAAO;YACL,OAAO,WAAW,SAAS,IAAI,CAAC,QAAQ,IAAI;YAC5C,gBAAgB,WAAW,SAAS,IAAI,CAAC,cAAc,IAAI;YAC3D,WAAW,WAAW,SAAS,IAAI,CAAC,SAAS,IAAI;YACjD,WAAW,WAAW,SAAS,IAAI,CAAC,GAAG,IAAI;YAC3C,WAAW,WAAW,SAAS,IAAI,CAAC,SAAS,IAAI;QACnD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,iBAAiB,OAC5B,OACA;IAOA,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,IAAI,CAAC,eAAe,aAAa;YAC1D,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,sDAAsD;QACtD,OAAO;YACL,QAAQ;YACR,YAAY;YACZ,aAAa;YACb,UAAU;QACZ;IACF;AACF", "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/services/bot/index.ts"], "sourcesContent": ["import axios, { AxiosInstance } from \"axios\";\r\n// Export types from wallets\r\nimport type {\r\n    Wallet,\r\n    CreateWalletResponse,\r\n    ImportWalletParams\r\n} from './wallets';\r\n\r\n\r\n\r\nconst baseURL = \"https://api.anubistrade.xyz\"\r\n\r\nconsole.log(\"Bot API URL:\", baseURL);\r\n\r\n// const baseURL = \"http://localhost:3001\"\r\n\r\n// Create an axios instance\r\nexport const api: AxiosInstance = axios.create({\r\n    baseURL,\r\n});\r\n\r\n// Example interceptor: log requests\r\napi.interceptors.request.use(\r\n    (config) => {\r\n        // You can add auth tokens or logging here\r\n        // console.log(\"Request:\", config);\r\n        return config;\r\n    },\r\n    (error) => Promise.reject(error)\r\n);\r\n\r\n// Example interceptor: log responses\r\napi.interceptors.response.use(\r\n    (response) => response,\r\n    (error) => {\r\n        // You can handle global errors here\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\ninterface VerifyOTPResponse {\r\n    message: string;\r\n    token: string;\r\n}\r\n\r\n// Verify OTP \r\nexport const terminalAuth = async (\r\n    otp: string,\r\n    tgUserId: string\r\n): Promise<VerifyOTPResponse> => {\r\n    try {\r\n        console.log(\"Axios Instance:\", api);\r\n        const response = await api.post(\"/auth/verify-otp\", { otp, tgUserId });\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error verifying OTP:\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\ninterface User {\r\n    walletId: number;\r\n    data: {\r\n        tokens: string[];\r\n        tradeValueInETH: { [key: string]: number };\r\n        referrals: any[];\r\n        referralRewardInETH: { [key: string]: number };\r\n        snipeValueInETH: { [key: string]: number };\r\n        snipes: any[];\r\n    };\r\n    settings: {\r\n        chainId: number;\r\n        slippage: number;\r\n        gasPrice: number;\r\n        wallets: { address: string }[];\r\n        viewedOnboarding: boolean;\r\n    };\r\n    token: string;\r\n    portfolioPage: number;\r\n    telegramUsername: string;\r\n}\r\n\r\n// Get User\r\nexport const getUser = async (token: string): Promise<User | { error: string; }> => {\r\n    try {\r\n        const response = await api.get(\"/bot/user\", {\r\n            headers: { Authorization: `Bearer ${token}` },\r\n        });\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error fetching tg user:\", error);\r\n        return { error: \"Error fetching tg user\" };\r\n    }\r\n};\r\n\r\ninterface Wallets {\r\n    address: `0x${string}`\r\n}\r\n\r\n// Export wallet types and functions\r\nexport type { Wallet, CreateWalletResponse, ImportWalletParams };\r\nexport {\r\n    getWallets,\r\n    createWallet,\r\n    importWallet,\r\n    setPrimaryWallet,\r\n    getWalletPrivateKey,\r\n    getWalletBalance,\r\n    exportWallet,\r\n    deleteWallet,\r\n    updateWalletName\r\n} from './wallets';\r\n\r\n// Export trading functions\r\nexport {\r\n    executeTrade,\r\n    executeSnipe,\r\n    executeSell,\r\n    getOpenOrders,\r\n    getOrderHistory,\r\n    getPositions,\r\n    cancelOrder\r\n} from './trading';\r\n\r\n\r\n\r\n// Get Settings\r\nexport const getSettings = async (token: string) => {\r\n    try {\r\n        const response = await api.get(\"/bot/user/settings\", {\r\n            headers: { Authorization: `Bearer ${token}` },\r\n        });\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error fetching settings\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\ninterface Settings {\r\n    chainId?: number;\r\n    slippage?: number;\r\n    gasPrice?: number;\r\n}\r\n\r\n// Update Settings\r\nexport const updateSettings = async (token: string, settings: Settings) => {\r\n    try {\r\n        const response = await api.post(\"/bot/user/settings\", settings, {\r\n            headers: { Authorization: `Bearer ${token}` },\r\n        });\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error updating settings\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\n// Get User's Native Balance\r\nexport const getNativeBalance = async (token: string) => {\r\n    try {\r\n        const response = await api.get(\"/bot/user/wallets/balance\", {\r\n            headers: { Authorization: `Bearer ${token}` },\r\n        });\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error fetching native balance\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\nexport const generateNewWallet = async (token: string) => {\r\n    try {\r\n        const response = await api.post(\r\n            \"/bot/wallet/create\",\r\n            {},\r\n            {\r\n                headers: { Authorization: `Bearer ${token}` },\r\n            }\r\n        );\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error generating new wallet\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\nexport const getUserPortfolio = async (token: string) => {\r\n    try {\r\n        const response = await api.get(\"/bot/user/portfolio\", {\r\n            headers: { Authorization: `Bearer ${token}` },\r\n        });\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error fetching user portfolio\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\n// Send 2FA Code\r\nexport const send2FA = async (token: string) => {\r\n    try {\r\n        const response = await api.post(\r\n            \"/bot/user/2fa/send\",\r\n            {},\r\n            {\r\n                headers: { Authorization: `Bearer ${token}` },\r\n            }\r\n        );\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error sending 2FA code\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\n// Verify 2FA Code\r\nexport const verify2FA = async (token: string, code: string) => {\r\n    try {\r\n        const response = await api.post(\r\n            \"/bot/user/2fa/verify\",\r\n            { code },\r\n            {\r\n                headers: { Authorization: `Bearer ${token}` },\r\n            }\r\n        );\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error verifying 2FA code\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\n\r\n// Wallet Import\r\nexport const importNewWallet = async (token: string, privateKey: string) => {\r\n    try {\r\n        const response = await api.post(\r\n            \"/bot/user/wallets/import\",\r\n            { privateKey },\r\n            {\r\n                headers: { Authorization: `Bearer ${token}` },\r\n            }\r\n        );\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error importing new wallet\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\n// Token Search\r\nexport const tokenSearchByContract = async (tokenContractAddress: `0x${string}`, token: string): Promise<\r\n    {\r\n        address: string,\r\n        name: string | null,\r\n        symbol: string | null,\r\n        balance: string,\r\n        priceUsd: number | null,\r\n        priceNative: number | null,\r\n        fdv: number | null,\r\n        liquidity: number | null,\r\n        volume24h: number | null,\r\n        priceChange24h: number | null,\r\n        url: string | null\r\n    } | {\r\n        error: string,\r\n        message: string\r\n    }\r\n> => {\r\n    try {\r\n        const response = await api.post(\r\n            \"/bot/user/token/search\", {\r\n            address: tokenContractAddress\r\n        },\r\n            {\r\n                headers: {\r\n                    Authorization: `Bearer ${token}`\r\n                }\r\n            }\r\n        )\r\n\r\n        return response.data;\r\n    } catch (error: any) {\r\n        return {\r\n            error: \"Token not found or fetch failed\",\r\n            message: \"Could not fetch token info. Please check the contract address and try again.\"\r\n        }\r\n    }\r\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAqGA;AAYA,2BAA2B;AAC3B;;AAxGA,MAAM,UAAU;AAEhB,QAAQ,GAAG,CAAC,gBAAgB;AAKrB,MAAM,MAAqB,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC3C;AACJ;AAEA,oCAAoC;AACpC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CACxB,CAAC;IACG,0CAA0C;IAC1C,mCAAmC;IACnC,OAAO;AACX,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;AAG9B,qCAAqC;AACrC,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CACzB,CAAC,WAAa,UACd,CAAC;IACG,oCAAoC;IACpC,OAAO,QAAQ,MAAM,CAAC;AAC1B;AASG,MAAM,eAAe,OACxB,KACA;IAEA,IAAI;QACA,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;YAAE;YAAK;QAAS;QACpE,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACV;AACJ;AAyBO,MAAM,UAAU,OAAO;IAC1B,IAAI;QACA,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,aAAa;YACxC,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAChD;QACA,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;YAAE,OAAO;QAAyB;IAC7C;AACJ;;;AAkCO,MAAM,cAAc,OAAO;IAC9B,IAAI;QACA,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,sBAAsB;YACjD,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAChD;QACA,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACV;AACJ;AASO,MAAM,iBAAiB,OAAO,OAAe;IAChD,IAAI;QACA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,sBAAsB,UAAU;YAC5D,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAChD;QACA,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACV;AACJ;AAGO,MAAM,mBAAmB,OAAO;IACnC,IAAI;QACA,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,6BAA6B;YACxD,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAChD;QACA,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACV;AACJ;AAEO,MAAM,oBAAoB,OAAO;IACpC,IAAI;QACA,MAAM,WAAW,MAAM,IAAI,IAAI,CAC3B,sBACA,CAAC,GACD;YACI,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAChD;QAEJ,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACV;AACJ;AAEO,MAAM,mBAAmB,OAAO;IACnC,IAAI;QACA,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,uBAAuB;YAClD,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAChD;QACA,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACV;AACJ;AAGO,MAAM,UAAU,OAAO;IAC1B,IAAI;QACA,MAAM,WAAW,MAAM,IAAI,IAAI,CAC3B,sBACA,CAAC,GACD;YACI,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAChD;QAEJ,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACV;AACJ;AAGO,MAAM,YAAY,OAAO,OAAe;IAC3C,IAAI;QACA,MAAM,WAAW,MAAM,IAAI,IAAI,CAC3B,wBACA;YAAE;QAAK,GACP;YACI,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAChD;QAEJ,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACV;AACJ;AAIO,MAAM,kBAAkB,OAAO,OAAe;IACjD,IAAI;QACA,MAAM,WAAW,MAAM,IAAI,IAAI,CAC3B,4BACA;YAAE;QAAW,GACb;YACI,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAChD;QAEJ,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACV;AACJ;AAGO,MAAM,wBAAwB,OAAO,sBAAqC;IAkB7E,IAAI;QACA,MAAM,WAAW,MAAM,IAAI,IAAI,CAC3B,0BAA0B;YAC1B,SAAS;QACb,GACI;YACI,SAAS;gBACL,eAAe,CAAC,OAAO,EAAE,OAAO;YACpC;QACJ;QAGJ,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAY;QACjB,OAAO;YACH,OAAO;YACP,SAAS;QACb;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/services/bot/wallets.ts"], "sourcesContent": ["import { api } from './index';\r\n\r\nexport interface Wallet {\r\n  address: `0x${string}`;\r\n  isPrimary?: boolean;\r\n  // Add other wallet properties as needed from the backend\r\n}\r\n\r\nexport interface CreateWalletResponse {\r\n  address: `0x${string}`;\r\n  privateKey: string;\r\n  phrase?: string;\r\n  message: string;\r\n}\r\n\r\nexport interface ImportWalletParams {\r\n  privateKey: string;\r\n  name?: string;\r\n}\r\n\r\n/**\r\n * Get all wallets for the authenticated user\r\n */\r\nexport const getWallets = async (token: string): Promise<Wallet[]> => {\r\n  try {\r\n    const response = await api.get<{ wallets: Wallet[] }>('/bot/user/wallets', {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n    return response.data.wallets || [];\r\n  } catch (error) {\r\n    console.error('Error fetching wallets:', error);\r\n    throw new Error('Failed to fetch wallets');\r\n  }\r\n};\r\n\r\n/**\r\n * Create a new wallet\r\n */\r\nexport const createWallet = async (token: string): Promise<CreateWalletResponse> => {\r\n  try {\r\n    const response = await api.post<CreateWalletResponse>(\r\n      '/bot/user/wallet/create',\r\n      {},\r\n      {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error creating wallet:', error);\r\n    throw new Error('Failed to create wallet');\r\n  }\r\n};\r\n\r\n/**\r\n * Import a wallet by private key\r\n */\r\nexport const importWallet = async (\r\n  token: string,\r\n  params: ImportWalletParams\r\n): Promise<Wallet> => {\r\n  try {\r\n    const response = await api.post<Wallet>(\r\n      '/bot/user/wallets/import',\r\n      { privateKey: params.privateKey },\r\n      {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error importing wallet:', error);\r\n    throw new Error('Failed to import wallet');\r\n  }\r\n};\r\n\r\n/**\r\n * Set a wallet as primary\r\n */\r\nexport const setPrimaryWallet = async (\r\n  token: string,\r\n  walletAddress: `0x${string}`\r\n): Promise<void> => {\r\n  try {\r\n    await api.post(\r\n      '/bot/user/wallets/primary',\r\n      { address: walletAddress },\r\n      {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error('Error setting primary wallet:', error);\r\n    throw new Error('Failed to set primary wallet');\r\n  }\r\n};\r\n\r\n/**\r\n * Get wallet private key\r\n * Note: This should be used with caution and proper security measures\r\n */\r\nexport const getWalletPrivateKey = async (\r\n  token: string,\r\n  walletAddress: `0x${string}`\r\n): Promise<{ privateKey: string }> => {\r\n  try {\r\n    const response = await api.get<{ privateKey: string }>(\r\n      `/bot/user/wallets/private/${walletAddress}`,\r\n      {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error getting wallet private key:', error);\r\n    throw new Error('Failed to get wallet private key');\r\n  }\r\n};\r\n\r\n/**\r\n * Get wallet balance for a specific chain\r\n */\r\nexport const getWalletBalance = async (\r\n  token: string,\r\n  walletAddress: `0x${string}`,\r\n  chainId: number\r\n): Promise<{\r\n  chainId: number;\r\n  chainName: string;\r\n  nativeBalance: string;\r\n  tokens: Array<{\r\n    address: string;\r\n    name: string | null;\r\n    symbol: string | null;\r\n    balance: string;\r\n    priceUsd: number | null;\r\n    priceNative: string | null;\r\n    isNative: boolean;\r\n  }>;\r\n}> => {\r\n  try {\r\n    const response = await api.get(\r\n      `/bot/user/wallets/balance?address=${walletAddress}&chainId=${chainId}`,\r\n      {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error getting wallet balance:', error);\r\n    throw new Error('Failed to get wallet balance');\r\n  }\r\n};\r\n\r\n/**\r\n * Export wallet data (private key and mnemonic)\r\n */\r\nexport const exportWallet = async (\r\n  token: string,\r\n  walletAddress: `0x${string}`,\r\n  password?: string\r\n): Promise<{\r\n  address: `0x${string}`;\r\n  privateKey: string;\r\n  mnemonic?: string;\r\n  encrypted?: boolean;\r\n}> => {\r\n  try {\r\n    const response = await api.post(\r\n      `/bot/user/wallets/export`,\r\n      {\r\n        address: walletAddress,\r\n        password: password\r\n      },\r\n      {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error exporting wallet:', error);\r\n    throw new Error('Failed to export wallet');\r\n  }\r\n};\r\n\r\n/**\r\n * Delete/Archive wallet\r\n */\r\nexport const deleteWallet = async (\r\n  token: string,\r\n  walletAddress: `0x${string}`\r\n): Promise<{ success: boolean; message: string }> => {\r\n  try {\r\n    const response = await api.delete(\r\n      `/bot/user/wallets/${walletAddress}`,\r\n      {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error deleting wallet:', error);\r\n    throw new Error('Failed to delete wallet');\r\n  }\r\n};\r\n\r\n/**\r\n * Update wallet name/label\r\n */\r\nexport const updateWalletName = async (\r\n  token: string,\r\n  walletAddress: `0x${string}`,\r\n  name: string\r\n): Promise<{ success: boolean; message: string }> => {\r\n  try {\r\n    const response = await api.patch(\r\n      `/bot/user/wallets/${walletAddress}`,\r\n      { name },\r\n      {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating wallet name:', error);\r\n    throw new Error('Failed to update wallet name');\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAAA;;AAuBO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,GAAG,CAAwB,qBAAqB;YACzE,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QACA,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,EAAE;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,IAAI,CAC7B,2BACA,CAAC,GACD;YACE,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,eAAe,OAC1B,OACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,IAAI,CAC7B,4BACA;YAAE,YAAY,OAAO,UAAU;QAAC,GAChC;YACE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,mBAAmB,OAC9B,OACA;IAEA,IAAI;QACF,MAAM,kJAAA,CAAA,MAAG,CAAC,IAAI,CACZ,6BACA;YAAE,SAAS;QAAc,GACzB;YACE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF;IAEJ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,IAAI,MAAM;IAClB;AACF;AAMO,MAAM,sBAAsB,OACjC,OACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,GAAG,CAC5B,CAAC,0BAA0B,EAAE,eAAe,EAC5C;YACE,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,mBAAmB,OAC9B,OACA,eACA;IAeA,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,GAAG,CAC5B,CAAC,kCAAkC,EAAE,cAAc,SAAS,EAAE,SAAS,EACvE;YACE,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,eAAe,OAC1B,OACA,eACA;IAOA,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,IAAI,CAC7B,CAAC,wBAAwB,CAAC,EAC1B;YACE,SAAS;YACT,UAAU;QACZ,GACA;YACE,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,eAAe,OAC1B,OACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,MAAM,CAC/B,CAAC,kBAAkB,EAAE,eAAe,EACpC;YACE,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,mBAAmB,OAC9B,OACA,eACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,kJAAA,CAAA,MAAG,CAAC,KAAK,CAC9B,CAAC,kBAAkB,EAAE,eAAe,EACpC;YAAE;QAAK,GACP;YACE,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/contexts/AuthContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\r\nimport { useCookies } from 'react-cookie';\r\nimport { useRouter } from 'next/navigation';\r\nimport { getUser, getWallets, getSettings, getNativeBalance, getUserPortfolio } from \"@/services/bot\";\r\n\r\ninterface AuthContextType {\r\n    data: any;\r\n    isLoggedIn: boolean;\r\n    token: string | null;\r\n    user: null;\r\n    loading: boolean;\r\n    logout: () => void;\r\n    storeCookie: (token: string) => void;\r\n    cacheProfile: (user: any) => void;\r\n    deleteProfile: () => void;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\r\n    const [isLoggedIn, setIsLoggedIn] = useState(false);\r\n    const [cookies, setCookie, removeCookie] = useCookies(['anubis_terminal']);\r\n    const [token, setToken] = useState<string | null>(null);\r\n    const [user, setUser] = useState<any | null>(null);\r\n    const [data, setData] = useState<any | null>(null);\r\n    const [loading, setLoading] = useState<boolean>(false);\r\n    const { push } = useRouter();\r\n\r\n    function storeCookie(token: string) {\r\n        setCookie('anubis_terminal', token, { path: '/', maxAge: 3600 * 24 * 7 }); // 7 days\r\n\r\n        setToken(token);\r\n        setIsLoggedIn(true);\r\n    }\r\n\r\n    function logout() {\r\n        removeCookie('anubis_terminal', { path: '/' });\r\n        setToken(null);\r\n        setIsLoggedIn(false);\r\n        push('/login');\r\n    }\r\n\r\n    function cacheProfile(user: any) {\r\n        window.localStorage.setItem(\"anubis_user\", JSON.stringify(user));\r\n        setUser(user);\r\n    }\r\n\r\n    function deleteProfile() {\r\n        window.localStorage.removeItem(\"anubis_user\");\r\n        setUser(null);\r\n    }\r\n\r\n    useEffect(() => {\r\n        if (cookies.anubis_terminal) {\r\n            setToken(cookies.anubis_terminal);\r\n            setIsLoggedIn(true);\r\n        }\r\n\r\n        const user = window.localStorage.getItem(\"anubis_user\");\r\n        if (user) {\r\n            setUser(JSON.parse(user));\r\n        }\r\n\r\n        // Make API Calls to Grab Info\r\n        if (isLoggedIn) {\r\n            setLoading(true);\r\n            (async () => {\r\n                try {\r\n                    const [userData, wallets, settings, balance, portfolio] = await Promise.all([\r\n                        getUser(token as string),\r\n                        getWallets(token as string),\r\n                        getSettings(token as string),\r\n                        getNativeBalance(token as string),\r\n                        getUserPortfolio(token as string)\r\n                    ]);\r\n\r\n                    console.log(\r\n                        userData,\r\n                        wallets,\r\n                        settings,\r\n                        balance,\r\n                        portfolio\r\n                    )\r\n\r\n                    setData({\r\n                        userData,\r\n                        wallets,\r\n                        settings,\r\n                        balance,\r\n                        portfolio\r\n                    });\r\n                } catch (error) {\r\n                    console.error(\"Error fetching user data\", error);\r\n                } finally {\r\n                    setLoading(false);\r\n                }\r\n            })();\r\n        }\r\n\r\n    }, [cookies.anubis_terminal])\r\n\r\n\r\n    return (\r\n        <AuthContext.Provider value={{ data, isLoggedIn, token, user, loading, logout, storeCookie, cacheProfile, deleteProfile }}>\r\n            {children}\r\n        </AuthContext.Provider>\r\n    );\r\n};\r\n\r\nexport const useAuth = (): AuthContextType => {\r\n    const context = useContext(AuthContext);\r\n    if (!context) {\r\n        throw new Error('useAuth must be used within an AuthProvider');\r\n    }\r\n    return context;\r\n};"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;AALA;;;;;AAmBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAkD,CAAC,EAAE,QAAQ,EAAE;;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,aAAa,GAAG,CAAA,GAAA,mKAAA,CAAA,aAAU,AAAD,EAAE;QAAC;KAAkB;IACzE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEzB,SAAS,YAAY,KAAa;QAC9B,UAAU,mBAAmB,OAAO;YAAE,MAAM;YAAK,QAAQ,OAAO,KAAK;QAAE,IAAI,SAAS;QAEpF,SAAS;QACT,cAAc;IAClB;IAEA,SAAS;QACL,aAAa,mBAAmB;YAAE,MAAM;QAAI;QAC5C,SAAS;QACT,cAAc;QACd,KAAK;IACT;IAEA,SAAS,aAAa,IAAS;QAC3B,OAAO,YAAY,CAAC,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;QAC1D,QAAQ;IACZ;IAEA,SAAS;QACL,OAAO,YAAY,CAAC,UAAU,CAAC;QAC/B,QAAQ;IACZ;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,QAAQ,eAAe,EAAE;gBACzB,SAAS,QAAQ,eAAe;gBAChC,cAAc;YAClB;YAEA,MAAM,OAAO,OAAO,YAAY,CAAC,OAAO,CAAC;YACzC,IAAI,MAAM;gBACN,QAAQ,KAAK,KAAK,CAAC;YACvB;YAEA,8BAA8B;YAC9B,IAAI,YAAY;gBACZ,WAAW;gBACX;8CAAC;wBACG,IAAI;4BACA,MAAM,CAAC,UAAU,SAAS,UAAU,SAAS,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gCACxE,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD,EAAE;gCACR,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD,EAAE;gCACX,CAAA,GAAA,kJAAA,CAAA,cAAW,AAAD,EAAE;gCACZ,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE;gCACjB,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE;6BACpB;4BAED,QAAQ,GAAG,CACP,UACA,SACA,UACA,SACA;4BAGJ,QAAQ;gCACJ;gCACA;gCACA;gCACA;gCACA;4BACJ;wBACJ,EAAE,OAAO,OAAO;4BACZ,QAAQ,KAAK,CAAC,4BAA4B;wBAC9C,SAAU;4BACN,WAAW;wBACf;oBACJ;iBAAC;YACL;QAEJ;iCAAG;QAAC,QAAQ,eAAe;KAAC;IAG5B,qBACI,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAY;YAAO;YAAM;YAAS;YAAQ;YAAa;YAAc;QAAc;kBACnH;;;;;;AAGb;GAxFa;;QAEkC,mKAAA,CAAA,aAAU;QAKpC,qIAAA,CAAA,YAAS;;;KAPjB;AA0FN,MAAM,UAAU;;IACnB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACV,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX;IANa", "debugId": null}}, {"offset": {"line": 1028, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/services/api/flooz.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\ninterface GetGainerResponse {\r\n    // Define the response type based on the actual API response structure\r\n    data: any;\r\n}\r\n\r\nexport interface TokenPriceData {\r\n    time: number;\r\n    price: string;\r\n}\r\n\r\nexport const getGainerTokenPairs = async (networks: string, time: string) => {\r\n    const url = \"https://api.flooz.trade/v2/trend\";\r\n    const params = {\r\n        networks: networks,\r\n        trendType: 'gainers',\r\n        volumePeriod: time,\r\n    };\r\n\r\n    const headers = {\r\n        accept: '*/*',\r\n        'accept-language': 'en-US,en;q=0.9',\r\n        authorization: `Bearer ${process.env.NEXT_PUBLIC_FLOOZ_API_KEY}`,\r\n        priority: 'u=1, i',\r\n        'save-data': 'on',\r\n        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',\r\n        'Referrer-Policy': 'strict-origin-when-cross-origin'\r\n    };\r\n\r\n    try {\r\n        const response = await axios.get(url, {\r\n            params,\r\n            headers\r\n        });\r\n        return response.data.results;\r\n    } catch (error) {\r\n        console.error('Error fetching new trends:', error);\r\n        throw error;\r\n    }\r\n};\r\n\r\n\r\nexport const getNewTokenPairs = async (networks: string, time: string) => {\r\n    const url = \"https://api.flooz.trade/v2/trend\";\r\n    const params = {\r\n        networks: networks,\r\n        trendType: 'newPairs',\r\n        volumePeriod: time,\r\n    };\r\n\r\n    const headers = {\r\n        accept: '*/*',\r\n        'accept-language': 'en-US,en;q=0.9',\r\n        authorization: `Bearer ${process.env.NEXT_PUBLIC_FLOOZ_API_KEY}`,\r\n        priority: 'u=1, i',\r\n        'save-data': 'on',\r\n        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',\r\n        'Referrer-Policy': 'strict-origin-when-cross-origin'\r\n    };\r\n\r\n    try {\r\n        const response = await axios.get(url, {\r\n            params,\r\n            headers\r\n        });\r\n        return response.data.results;\r\n    } catch (error) {\r\n        console.error('Error fetching new trends:', error);\r\n        throw error;\r\n    }\r\n};\r\n\r\nexport const getTrendingTokens = async (networks: string, time: string) => {\r\n    const url = \"https://api.flooz.trade/v2/trend\";\r\n    const params = {\r\n        networks: networks,\r\n        volumePeriod: time,\r\n    };\r\n\r\n    const headers = {\r\n        accept: '*/*',\r\n        'accept-language': 'en-US,en;q=0.9',\r\n        authorization: `Bearer ${process.env.NEXT_PUBLIC_FLOOZ_API_KEY}`,\r\n        priority: 'u=1, i',\r\n        'save-data': 'on',\r\n        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',\r\n        'Referrer-Policy': 'strict-origin-when-cross-origin'\r\n    };\r\n\r\n    try {\r\n        const response = await axios.get(url, {\r\n            params,\r\n            headers\r\n        });\r\n        return response.data.results;\r\n    } catch (error) {\r\n        console.error('Error fetching new trends:', error);\r\n        throw error;\r\n    }\r\n};\r\n\r\nexport const getTokenData = async (networks: string, address: `0x${string}`) => {\r\n    const url = \"https://api.flooz.trade/v2/trend\";\r\n\r\n    const body = {\r\n        filters: {\r\n            networks: [networks]\r\n        },\r\n        tokens: [address]\r\n    };\r\n\r\n    const headers = {\r\n        accept: '*/*',\r\n        'accept-language': 'en-US,en;q=0.9',\r\n        authorization: `Bearer ${process.env.NEXT_PUBLIC_FLOOZ_API_KEY}`,\r\n        priority: 'u=1, i',\r\n        'save-data': 'on',\r\n        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',\r\n        'Referrer-Policy': 'strict-origin-when-cross-origin'\r\n    };\r\n\r\n    try {\r\n        const response = await axios.post(url, body, { headers });\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error('Error fetching token data:', error);\r\n        throw error;\r\n    }\r\n}\r\n\r\n\r\nexport const getTokenPriceHistory = async (\r\n    network: string,\r\n    address: `0x${string}`\r\n): Promise<TokenPriceData[]> => {\r\n    const url = `https://api.flooz.trade/v2/tokens/${address}/price`;\r\n\r\n    // Get current date in ISO format\r\n    const till = new Date().toISOString();\r\n\r\n    const params = {\r\n        network,\r\n        till,\r\n        countBack: 300,\r\n        resolution: 5\r\n    };\r\n\r\n    const headers = {\r\n        accept: 'application/json, text/plain, */*',\r\n        'accept-language': 'en-US,en;q=0.9',\r\n        authorization: `Bearer ${process.env.NEXT_PUBLIC_FLOOZ_API_KEY}`,\r\n        priority: 'u=1, i',\r\n        'save-data': 'on',\r\n        'sec-ch-ua': '\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"',\r\n        'sec-ch-ua-mobile': '?1',\r\n        'sec-ch-ua-platform': '\"Android\"',\r\n        'sec-fetch-dest': 'empty',\r\n        'sec-fetch-mode': 'cors',\r\n        'sec-fetch-site': 'cross-site',\r\n        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',\r\n        'Referer': 'https://flooz.xyz/',\r\n        'Referrer-Policy': 'strict-origin-when-cross-origin'\r\n    };\r\n\r\n    try {\r\n        const response = await axios.get(url, {\r\n            params,\r\n            headers\r\n        });\r\n        return response.data.data;\r\n    } catch (error) {\r\n        console.error('Error fetching token price history:', error);\r\n        throw error;\r\n    }\r\n};"], "names": [], "mappings": ";;;;;;;AAuBiC;AAvBjC;;AAYO,MAAM,sBAAsB,OAAO,UAAkB;IACxD,MAAM,MAAM;IACZ,MAAM,SAAS;QACX,UAAU;QACV,WAAW;QACX,cAAc;IAClB;IAEA,MAAM,UAAU;QACZ,QAAQ;QACR,mBAAmB;QACnB,eAAe,CAAC,OAAO,i8BAAyC;QAChE,UAAU;QACV,aAAa;QACb,kBAAkB;QAClB,mBAAmB;IACvB;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;YAClC;YACA;QACJ;QACA,OAAO,SAAS,IAAI,CAAC,OAAO;IAChC,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACV;AACJ;AAGO,MAAM,mBAAmB,OAAO,UAAkB;IACrD,MAAM,MAAM;IACZ,MAAM,SAAS;QACX,UAAU;QACV,WAAW;QACX,cAAc;IAClB;IAEA,MAAM,UAAU;QACZ,QAAQ;QACR,mBAAmB;QACnB,eAAe,CAAC,OAAO,i8BAAyC;QAChE,UAAU;QACV,aAAa;QACb,kBAAkB;QAClB,mBAAmB;IACvB;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;YAClC;YACA;QACJ;QACA,OAAO,SAAS,IAAI,CAAC,OAAO;IAChC,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACV;AACJ;AAEO,MAAM,oBAAoB,OAAO,UAAkB;IACtD,MAAM,MAAM;IACZ,MAAM,SAAS;QACX,UAAU;QACV,cAAc;IAClB;IAEA,MAAM,UAAU;QACZ,QAAQ;QACR,mBAAmB;QACnB,eAAe,CAAC,OAAO,i8BAAyC;QAChE,UAAU;QACV,aAAa;QACb,kBAAkB;QAClB,mBAAmB;IACvB;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;YAClC;YACA;QACJ;QACA,OAAO,SAAS,IAAI,CAAC,OAAO;IAChC,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACV;AACJ;AAEO,MAAM,eAAe,OAAO,UAAkB;IACjD,MAAM,MAAM;IAEZ,MAAM,OAAO;QACT,SAAS;YACL,UAAU;gBAAC;aAAS;QACxB;QACA,QAAQ;YAAC;SAAQ;IACrB;IAEA,MAAM,UAAU;QACZ,QAAQ;QACR,mBAAmB;QACnB,eAAe,CAAC,OAAO,i8BAAyC;QAChE,UAAU;QACV,aAAa;QACb,kBAAkB;QAClB,mBAAmB;IACvB;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK,MAAM;YAAE;QAAQ;QACvD,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACV;AACJ;AAGO,MAAM,uBAAuB,OAChC,SACA;IAEA,MAAM,MAAM,CAAC,kCAAkC,EAAE,QAAQ,MAAM,CAAC;IAEhE,iCAAiC;IACjC,MAAM,OAAO,IAAI,OAAO,WAAW;IAEnC,MAAM,SAAS;QACX;QACA;QACA,WAAW;QACX,YAAY;IAChB;IAEA,MAAM,UAAU;QACZ,QAAQ;QACR,mBAAmB;QACnB,eAAe,CAAC,OAAO,i8BAAyC;QAChE,UAAU;QACV,aAAa;QACb,aAAa;QACb,oBAAoB;QACpB,sBAAsB;QACtB,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,WAAW;QACX,mBAAmB;IACvB;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;YAClC;YACA;QACJ;QACA,OAAO,SAAS,IAAI,CAAC,IAAI;IAC7B,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACV;AACJ", "debugId": null}}, {"offset": {"line": 1195, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/hooks/useUserData.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\r\nimport { getUser, getWallets, getSettings, getNativeBalance, getUserPortfolio } from '@/services/bot';\r\n\r\n// Cache configuration\r\nconst CACHE_KEY_PREFIX = 'anubis_user_data_';\r\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\r\n\r\ninterface TokenData {\r\n    address: string;\r\n    name: string | null;\r\n    symbol: string | null;\r\n    balance: string;\r\n    priceUsd: number | null;\r\n    priceNative: number | null;\r\n    fdv: number | null;\r\n    liquidity: number | null;\r\n    volume24h: number | null;\r\n    priceChange24h: number | null;\r\n    url: string | null;\r\n}\r\n\r\ninterface Wallet {\r\n    address: string;\r\n    phrase?: string;\r\n    privateKey?: string;\r\n}\r\n\r\ninterface Settings {\r\n    chainId: number;\r\n    slippage: number;\r\n    gasPrice: number;\r\n    wallets: Wallet[];\r\n    viewedOnboarding: boolean;\r\n}\r\n\r\ninterface TradeValue {\r\n    [chainId: string]: number;\r\n}\r\n\r\ninterface UserData {\r\n    user: {\r\n        tgUserId: string;\r\n        walletId: number;\r\n        data: {\r\n            tokens: string[];\r\n            tradeValueInETH: TradeValue;\r\n            referrals: any[];\r\n            referralRewardInETH: TradeValue;\r\n            snipeValueInETH: TradeValue;\r\n            snipes: any[];\r\n        };\r\n        settings: Settings;\r\n        token: string;\r\n        portfolioPage: number;\r\n        telegramUsername: string;\r\n    };\r\n    wallets: Wallet[];\r\n    settings: Settings;\r\n    balance: string;\r\n    portfolio: TokenData[];\r\n}\r\n\r\nfunction getCacheKey(token: string): string {\r\n    return `${CACHE_KEY_PREFIX}${token}`;\r\n}\r\n\r\nfunction getCachedUserData(token: string): UserData | null {\r\n    try {\r\n        const cacheKey = getCacheKey(token);\r\n        const cachedData = localStorage.getItem(cacheKey);\r\n        if (!cachedData) return null;\r\n\r\n        const { data, timestamp } = JSON.parse(cachedData);\r\n        const now = new Date().getTime();\r\n\r\n        // Check if cache is still valid\r\n        if (now - timestamp < CACHE_DURATION) {\r\n            return data;\r\n        }\r\n    } catch (error) {\r\n        console.error('Error reading from cache:', error);\r\n    }\r\n    return null;\r\n}\r\n\r\nfunction cacheUserData(token: string, data: UserData): void {\r\n    try {\r\n        const cacheKey = getCacheKey(token);\r\n        const cacheData = {\r\n            data,\r\n            timestamp: new Date().getTime()\r\n        };\r\n        localStorage.setItem(cacheKey, JSON.stringify(cacheData));\r\n    } catch (error) {\r\n        console.error('Error writing to cache:', error);\r\n    }\r\n}\r\n\r\nexport function useUserData(token: string | null) {\r\n    const [data, setData] = useState<UserData | null>(null);\r\n    const [loading, setLoading] = useState(false);\r\n    const [error, setError] = useState<Error | null>(null);\r\n\r\n    const fetchUserData = useCallback(async () => {\r\n        console.log(\"Fetching user data...\");\r\n        if (!token) {\r\n            setData(null);\r\n            setError(null);\r\n            return;\r\n        }\r\n\r\n        // Try to get data from cache first\r\n        const cachedData = getCachedUserData(token);\r\n        if (cachedData) {\r\n            console.log(\"Using cached user data\");\r\n            setData(cachedData);\r\n            setLoading(false);\r\n            return;\r\n        }\r\n\r\n        setLoading(true);\r\n        setError(null);\r\n        \r\n        try {\r\n            const [userData, wallets, settings, balance, portfolio] = await Promise.all([\r\n                getUser(token),\r\n                getWallets(token),\r\n                getSettings(token),\r\n                getNativeBalance(token),\r\n                getUserPortfolio(token)\r\n            ]);\r\n            \r\n            const userDataResponse = { ...userData, ...wallets, ...settings, ...balance, ...portfolio };\r\n            \r\n            // Cache the response\r\n            cacheUserData(token, userDataResponse);\r\n            setData(userDataResponse);\r\n\r\n        } catch (err: any) {\r\n            console.error('Error fetching user data:', err);\r\n            setError(err);\r\n            setData(null);\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, [token]);\r\n\r\n    const refetch = useCallback(async () => {\r\n        if (!token) return null;\r\n        \r\n        setLoading(true);\r\n        setError(null);\r\n        \r\n        try {\r\n            const [userData, wallets, settings, balance, portfolio] = await Promise.all([\r\n                getUser(token),\r\n                getWallets(token),\r\n                getSettings(token),\r\n                getNativeBalance(token),\r\n                getUserPortfolio(token)\r\n            ]);\r\n            \r\n            const userDataResponse = { ...userData, ...wallets, ...settings, ...balance, ...portfolio };\r\n            \r\n            // Update cache with fresh data\r\n            cacheUserData(token, userDataResponse);\r\n            setData(userDataResponse);\r\n            return userDataResponse;\r\n        } catch (err) {\r\n            console.error('Error refetching user data:', err);\r\n            setError(err as Error);\r\n            return null;\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, [token]);\r\n\r\n    useEffect(() => {\r\n        // Only set loading to true if we don't have cached data\r\n        if (!token || !getCachedUserData(token)) {\r\n            setLoading(true);\r\n        }\r\n        setError(null);\r\n        fetchUserData();\r\n    }, [token, fetchUserData]);\r\n\r\n    return { data, loading, error, refetch };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AAAA;;;;AAEA,sBAAsB;AACtB,MAAM,mBAAmB;AACzB,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAyDlD,SAAS,YAAY,KAAa;IAC9B,OAAO,GAAG,mBAAmB,OAAO;AACxC;AAEA,SAAS,kBAAkB,KAAa;IACpC,IAAI;QACA,MAAM,WAAW,YAAY;QAC7B,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,CAAC,YAAY,OAAO;QAExB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,KAAK,KAAK,CAAC;QACvC,MAAM,MAAM,IAAI,OAAO,OAAO;QAE9B,gCAAgC;QAChC,IAAI,MAAM,YAAY,gBAAgB;YAClC,OAAO;QACX;IACJ,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,6BAA6B;IAC/C;IACA,OAAO;AACX;AAEA,SAAS,cAAc,KAAa,EAAE,IAAc;IAChD,IAAI;QACA,MAAM,WAAW,YAAY;QAC7B,MAAM,YAAY;YACd;YACA,WAAW,IAAI,OAAO,OAAO;QACjC;QACA,aAAa,OAAO,CAAC,UAAU,KAAK,SAAS,CAAC;IAClD,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,2BAA2B;IAC7C;AACJ;AAEO,SAAS,YAAY,KAAoB;;IAC5C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC9B,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,OAAO;gBACR,QAAQ;gBACR,SAAS;gBACT;YACJ;YAEA,mCAAmC;YACnC,MAAM,aAAa,kBAAkB;YACrC,IAAI,YAAY;gBACZ,QAAQ,GAAG,CAAC;gBACZ,QAAQ;gBACR,WAAW;gBACX;YACJ;YAEA,WAAW;YACX,SAAS;YAET,IAAI;gBACA,MAAM,CAAC,UAAU,SAAS,UAAU,SAAS,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACxE,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD,EAAE;oBACR,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD,EAAE;oBACX,CAAA,GAAA,kJAAA,CAAA,cAAW,AAAD,EAAE;oBACZ,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE;oBACjB,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE;iBACpB;gBAED,MAAM,mBAAmB;oBAAE,GAAG,QAAQ;oBAAE,GAAG,OAAO;oBAAE,GAAG,QAAQ;oBAAE,GAAG,OAAO;oBAAE,GAAG,SAAS;gBAAC;gBAE1F,qBAAqB;gBACrB,cAAc,OAAO;gBACrB,QAAQ;YAEZ,EAAE,OAAO,KAAU;gBACf,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,SAAS;gBACT,QAAQ;YACZ,SAAU;gBACN,WAAW;YACf;QACJ;iDAAG;QAAC;KAAM;IAEV,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YACxB,IAAI,CAAC,OAAO,OAAO;YAEnB,WAAW;YACX,SAAS;YAET,IAAI;gBACA,MAAM,CAAC,UAAU,SAAS,UAAU,SAAS,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACxE,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD,EAAE;oBACR,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD,EAAE;oBACX,CAAA,GAAA,kJAAA,CAAA,cAAW,AAAD,EAAE;oBACZ,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE;oBACjB,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE;iBACpB;gBAED,MAAM,mBAAmB;oBAAE,GAAG,QAAQ;oBAAE,GAAG,OAAO;oBAAE,GAAG,QAAQ;oBAAE,GAAG,OAAO;oBAAE,GAAG,SAAS;gBAAC;gBAE1F,+BAA+B;gBAC/B,cAAc,OAAO;gBACrB,QAAQ;gBACR,OAAO;YACX,EAAE,OAAO,KAAK;gBACV,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,SAAS;gBACT,OAAO;YACX,SAAU;gBACN,WAAW;YACf;QACJ;2CAAG;QAAC;KAAM;IAEV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,wDAAwD;YACxD,IAAI,CAAC,SAAS,CAAC,kBAAkB,QAAQ;gBACrC,WAAW;YACf;YACA,SAAS;YACT;QACJ;gCAAG;QAAC;QAAO;KAAc;IAEzB,OAAO;QAAE;QAAM;QAAS;QAAO;IAAQ;AAC3C;GAzFgB", "debugId": null}}, {"offset": {"line": 1356, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/contexts/Web3Context.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';\r\nimport { chains } from '@/utils/data';\r\nimport { useAuth } from \"./AuthContext\";\r\nimport { getTokenData } from '@/services/api/flooz';\r\nimport { useUserData } from '@/hooks/useUserData';\r\n\r\n// Use the type from data.ts\r\nexport type Chain = typeof chains[number];\r\n\r\ninterface Web3ContextType {\r\n    chainList: Chain[];\r\n    selectedChain: Chain | undefined;\r\n    setSelectedChain: (chain: Chain) => void;\r\n    recentlyViewed: { address: `0x${string}`, chain: string, data: any }[];\r\n    addRecentlyViewed: (address: `0x${string}`, chain: string) => Promise<void>;\r\n    removeRecentlyViewed: (index: number) => void;\r\n}\r\n\r\nconst Web3Context = createContext<Web3ContextType | undefined>(undefined);\r\n\r\nexport const Web3Provider: React.FC<{ children: ReactNode }> = ({ children }) => {\r\n    const [chainList] = useState<Chain[]>(chains);\r\n    const { token } = useAuth();\r\n    const { data } = useUserData(token);\r\n    const [selectedChain, setSelectedChain] = useState<Chain | undefined>(undefined);\r\n\r\n    // UseEffect to set selected chain from API\r\n    useEffect(() => {\r\n        if (data?.settings?.chainId) {\r\n            const locatedChain = chainList.find(c => c.chainId === data.settings.chainId);\r\n            if (locatedChain) {\r\n                setSelectedChain(locatedChain);\r\n            } else {\r\n                setSelectedChain(chains[0]); // fallback if chainId is invalid\r\n            }\r\n        } else if (data && !data?.settings?.chainId) {\r\n            setSelectedChain(chains[0]); // fallback if no chainId at all\r\n        }\r\n        // else: leave as undefined (loading)\r\n    }, [data?.settings?.chainId, data, chainList]);\r\n\r\n    // Initialize state from localStorage\r\n    const [recentlyViewed, setRecentlyViewed] = useState<{ address: `0x${string}`, chain: string, data: any }[]>(() => {\r\n        if (typeof window !== 'undefined') {\r\n            const stored = localStorage.getItem('recentlyViewed');\r\n            return stored ? JSON.parse(stored) : [];\r\n        }\r\n        return [];\r\n    });\r\n\r\n    // Update localStorage whenever recentlyViewed changes\r\n    useEffect(() => {\r\n        if (typeof window !== 'undefined') {\r\n            localStorage.setItem('recentlyViewed', JSON.stringify(recentlyViewed));\r\n        }\r\n    }, [recentlyViewed]);\r\n\r\n    const addRecentlyViewed = useCallback(async (address: `0x${string}`, chain: string) => {\r\n        setRecentlyViewed(prev => {\r\n            // Check if item with same address and chain already exists\r\n            const exists = prev.some(item => item.address === address && item.chain === chain);\r\n            if (exists) {\r\n                return prev;\r\n            }\r\n            // Optimistically add a placeholder while fetching\r\n            return [...prev, { address, chain, data: null }];\r\n        });\r\n        try {\r\n            const data = await getTokenData(chain, address);\r\n            setRecentlyViewed(prev => {\r\n                // Replace the placeholder with the real data\r\n                return prev.map(item =>\r\n                    item.address === address && item.chain === chain\r\n                        ? { ...item, data }\r\n                        : item\r\n                );\r\n            });\r\n        } catch (error) {\r\n            // Optionally handle error (e.g., remove placeholder)\r\n            setRecentlyViewed(prev => prev.filter(item => !(item.address === address && item.chain === chain && item.data === null)));\r\n        }\r\n    }, []);\r\n\r\n    const removeRecentlyViewed = (index: number) => {\r\n        setRecentlyViewed(prev => {\r\n            const newState = prev.filter((_, i) => i !== index);\r\n            if (typeof window !== 'undefined') {\r\n                localStorage.setItem('recentlyViewed', JSON.stringify(newState));\r\n            }\r\n            return newState;\r\n        });\r\n    }\r\n\r\n    return (\r\n        <Web3Context.Provider value={{ chainList, selectedChain, setSelectedChain, recentlyViewed, addRecentlyViewed, removeRecentlyViewed }}>\r\n            {children}\r\n        </Web3Context.Provider>\r\n    );\r\n};\r\n\r\nexport const useWeb3 = (): Web3ContextType => {\r\n    const context = useContext(Web3Context);\r\n    if (!context) {\r\n        throw new Error('useWeb3 must be used within a Web3Provider');\r\n    }\r\n    return context;\r\n};"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAoBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAkD,CAAC,EAAE,QAAQ,EAAE;;IACxE,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,uHAAA,CAAA,SAAM;IAC5C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAEtE,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,MAAM,UAAU,SAAS;gBACzB,MAAM,eAAe,UAAU,IAAI;2DAAC,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,QAAQ,CAAC,OAAO;;gBAC5E,IAAI,cAAc;oBACd,iBAAiB;gBACrB,OAAO;oBACH,iBAAiB,uHAAA,CAAA,SAAM,CAAC,EAAE,GAAG,iCAAiC;gBAClE;YACJ,OAAO,IAAI,QAAQ,CAAC,MAAM,UAAU,SAAS;gBACzC,iBAAiB,uHAAA,CAAA,SAAM,CAAC,EAAE,GAAG,gCAAgC;YACjE;QACA,qCAAqC;QACzC;iCAAG;QAAC,MAAM,UAAU;QAAS;QAAM;KAAU;IAE7C,qCAAqC;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;iCAA0D;YACzG,wCAAmC;gBAC/B,MAAM,SAAS,aAAa,OAAO,CAAC;gBACpC,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU,EAAE;YAC3C;;QAEJ;;IAEA,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,wCAAmC;gBAC/B,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;YAC1D;QACJ;iCAAG;QAAC;KAAe;IAEnB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAAO,SAAwB;YACjE;+DAAkB,CAAA;oBACd,2DAA2D;oBAC3D,MAAM,SAAS,KAAK,IAAI;8EAAC,CAAA,OAAQ,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,KAAK;;oBAC5E,IAAI,QAAQ;wBACR,OAAO;oBACX;oBACA,kDAAkD;oBAClD,OAAO;2BAAI;wBAAM;4BAAE;4BAAS;4BAAO,MAAM;wBAAK;qBAAE;gBACpD;;YACA,IAAI;gBACA,MAAM,OAAO,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBACvC;mEAAkB,CAAA;wBACd,6CAA6C;wBAC7C,OAAO,KAAK,GAAG;2EAAC,CAAA,OACZ,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,KAAK,QACrC;oCAAE,GAAG,IAAI;oCAAE;gCAAK,IAChB;;oBAEd;;YACJ,EAAE,OAAO,OAAO;gBACZ,qDAAqD;gBACrD;mEAAkB,CAAA,OAAQ,KAAK,MAAM;2EAAC,CAAA,OAAQ,CAAC,CAAC,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK,IAAI;;;YAC1H;QACJ;sDAAG,EAAE;IAEL,MAAM,uBAAuB,CAAC;QAC1B,kBAAkB,CAAA;YACd,MAAM,WAAW,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC7C,wCAAmC;gBAC/B,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;YAC1D;YACA,OAAO;QACX;IACJ;IAEA,qBACI,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAW;YAAe;YAAkB;YAAgB;YAAmB;QAAqB;kBAC9H;;;;;;AAGb;GA9Ea;;QAES,kIAAA,CAAA,UAAO;QACR,8HAAA,CAAA,cAAW;;;KAHnB;AAgFN,MAAM,UAAU;;IACnB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACV,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX;IANa", "debugId": null}}, {"offset": {"line": 1520, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Preloader.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\n\ninterface PreloaderProps {\n  duration?: number;\n  onComplete?: () => void;\n}\n\nexport default function Preloader({ duration = 2000, onComplete }: PreloaderProps) {\n  const [isLoading, setIsLoading] = useState(true);\n  const [progress, setProgress] = useState(0);\n\n  useEffect(() => {\n    const startTime = Date.now();\n\n    const updateProgress = () => {\n      const elapsed = Date.now() - startTime;\n      const newProgress = Math.min((elapsed / duration) * 100, 100);\n\n      setProgress(newProgress);\n\n      if (newProgress >= 100) {\n        setTimeout(() => {\n          setIsLoading(false);\n          onComplete?.();\n        }, 200); // Small delay before hiding\n      } else {\n        requestAnimationFrame(updateProgress);\n      }\n    };\n\n    requestAnimationFrame(updateProgress);\n  }, [duration, onComplete]);\n\n  return (\n    <AnimatePresence>\n      {isLoading && (\n        <motion.div\n          initial={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          transition={{ duration: 0.3 }}\n          className=\"fixed top-0 left-0 right-0 z-50 bg-transparent\"\n        >\n          {/* Preloader line */}\n          <div className=\"w-full h-1 bg-transparent relative overflow-hidden\">\n            <motion.div\n              className=\"h-full bg-white\"\n              initial={{ width: \"0%\" }}\n              animate={{ width: `${progress}%` }}\n              transition={{ ease: \"easeOut\", duration: 0.1 }}\n            />\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAUe,SAAS,UAAU,EAAE,WAAW,IAAI,EAAE,UAAU,EAAkB;;IAC/E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,YAAY,KAAK,GAAG;YAE1B,MAAM;sDAAiB;oBACrB,MAAM,UAAU,KAAK,GAAG,KAAK;oBAC7B,MAAM,cAAc,KAAK,GAAG,CAAC,AAAC,UAAU,WAAY,KAAK;oBAEzD,YAAY;oBAEZ,IAAI,eAAe,KAAK;wBACtB;kEAAW;gCACT,aAAa;gCACb;4BACF;iEAAG,MAAM,4BAA4B;oBACvC,OAAO;wBACL,sBAAsB;oBACxB;gBACF;;YAEA,sBAAsB;QACxB;8BAAG;QAAC;QAAU;KAAW;IAEzB,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;sBAGV,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,OAAO;oBAAK;oBACvB,SAAS;wBAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oBAAC;oBACjC,YAAY;wBAAE,MAAM;wBAAW,UAAU;oBAAI;;;;;;;;;;;;;;;;;;;;;AAO3D;GAhDwB;KAAA", "debugId": null}}, {"offset": {"line": 1622, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/app/layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, Orbitron, Space_Grotesk } from \"next/font/google\";\r\nimport \"./globals.css\";\r\nimport { Web3Provider } from \"@/contexts/Web3Context\";\r\nimport { AuthProvider } from \"@/contexts/AuthContext\";\r\nimport { ToastContainer } from \"react-toastify\";\r\nimport { motion } from \"framer-motion\";\r\nimport { IoMdClose } from \"react-icons/io\";\r\nimport { CookiesProvider } from 'react-cookie';\r\nimport Preloader from \"@/components/Preloader\";\r\n\r\nconst geistSans = Geist({\r\n  variable: \"--font-geist-sans\",\r\n  subsets: [\"latin\"],\r\n  display: \"swap\",\r\n});\r\n\r\nconst geistMono = Geist_Mono({\r\n  variable: \"--font-geist-mono\",\r\n  subsets: [\"latin\"],\r\n  display: \"swap\",\r\n});\r\n\r\nconst orbitron = Orbitron({\r\n  variable: \"--font-orbitron\",\r\n  subsets: [\"latin\"],\r\n  display: \"swap\",\r\n});\r\n\r\nconst spaceGrotesk = Space_Grotesk({\r\n  variable: \"--font-space-grotesk\",\r\n  subsets: [\"latin\"],\r\n  display: \"swap\",\r\n});\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  const [showPreloader, setShowPreloader] = useState(true);\r\n\r\n  useEffect(() => {\r\n    document.title = \"Anubis | Snipe and Sell Tokens On-Chain Instantly\";\r\n    const metaDescription = document.querySelector('meta[name=\"description\"]');\r\n    if (metaDescription) {\r\n      metaDescription.setAttribute('content', 'Snipe and Sell Tokens On-Chain Instantly');\r\n    }\r\n  }, []);\r\n\r\n  const handlePreloaderComplete = () => {\r\n    setShowPreloader(false);\r\n  };\r\n\r\n  return (\r\n    <html lang=\"en\">\r\n      <body\r\n        className={`${geistSans.variable} ${geistMono.variable} ${orbitron.variable} ${spaceGrotesk.variable} antialiased`}\r\n      >\r\n        <CookiesProvider>\r\n          <AuthProvider>\r\n            <Web3Provider>\r\n              {showPreloader && (\r\n                <Preloader\r\n                  duration={2000}\r\n                  onComplete={handlePreloaderComplete}\r\n                />\r\n              )}\r\n              {children}\r\n              <ToastContainer\r\n                position=\"bottom-right\"\r\n                autoClose={3000}\r\n                hideProgressBar={false}\r\n                newestOnTop={true}\r\n                closeOnClick\r\n                rtl={false}\r\n                pauseOnFocusLoss\r\n                draggable\r\n                pauseOnHover\r\n                theme=\"dark\"\r\n                toastClassName=\"font-orbitron bg-black/95 border border-white/20 rounded-sm backdrop-blur-xl shadow-lg shadow-black/20\"\r\n                className=\"text-xs tracking-wide\"\r\n                progressClassName=\"bg-gradient-to-r from-white/20 via-white to-white/20\"\r\n                closeButton={({ closeToast }) => (\r\n                  <motion.button\r\n                    className=\"text-white/70 hover:text-white transition-colors ml-auto\"\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                    onClick={closeToast}\r\n                  >\r\n                    <IoMdClose className=\"text-xs\" />\r\n                  </motion.button>\r\n                )}\r\n              />\r\n            </Web3Provider>\r\n          </AuthProvider>\r\n        </CookiesProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;;;;AAqCe,SAAS,WAAW,EACjC,QAAQ,EAGR;;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,SAAS,KAAK,GAAG;YACjB,MAAM,kBAAkB,SAAS,aAAa,CAAC;YAC/C,IAAI,iBAAiB;gBACnB,gBAAgB,YAAY,CAAC,WAAW;YAC1C;QACF;+BAAG,EAAE;IAEL,MAAM,0BAA0B;QAC9B,iBAAiB;IACnB;IAEA,qBACE,6LAAC;QAAK,MAAK;kBACT,cAAA,6LAAC;YACC,WAAW,GAAG,4IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,+IAAA,CAAA,UAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,oJAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;sBAElH,cAAA,6LAAC,mKAAA,CAAA,kBAAe;0BACd,cAAA,6LAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,kIAAA,CAAA,eAAY;;4BACV,+BACC,6LAAC,kIAAA,CAAA,UAAS;gCACR,UAAU;gCACV,YAAY;;;;;;4BAGf;0CACD,6LAAC,sJAAA,CAAA,iBAAc;gCACb,UAAS;gCACT,WAAW;gCACX,iBAAiB;gCACjB,aAAa;gCACb,YAAY;gCACZ,KAAK;gCACL,gBAAgB;gCAChB,SAAS;gCACT,YAAY;gCACZ,OAAM;gCACN,gBAAe;gCACf,WAAU;gCACV,mBAAkB;gCAClB,aAAa,CAAC,EAAE,UAAU,EAAE,iBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS;kDAET,cAAA,6LAAC,iJAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzC;GAjEwB;KAAA", "debugId": null}}]}