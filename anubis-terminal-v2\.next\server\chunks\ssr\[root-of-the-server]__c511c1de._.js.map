{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Wallets/WalletCard/index.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FiCopy, FiExternalLink, FiEdit2, FiTrash2, FiDownload, FiUpload } from 'react-icons/fi';\nimport { FaEthereum } from 'react-icons/fa';\nimport { SiBinance } from 'react-icons/si';\nimport { useClickAway } from 'react-use';\n\n// Anubis theme colors\nconst ANUBIS_THEME = {\n  primary: '#d4af37', // Gold\n  secondary: '#1a1a1a', // Dark background\n  accent: '#8b5a2b', // Brown accent\n  text: '#f0f0f0', // Light text\n  cardBg: 'rgba(26, 26, 26, 0.8)', // Semi-transparent dark\n  border: 'rgba(212, 175, 55, 0.2)', // Gold border\n  hover: 'rgba(212, 175, 55, 0.1)', // Gold hover\n};\n\nconst truncateAddress = (address: string, start = 6, end = 4) => {\n  if (!address) return '';\n  return `${address.slice(0, start)}...${address.slice(-end)}`;\n};\n\nconst getChainIcon = (chain: string) => {\n  switch (chain.toLowerCase()) {\n    case 'eth':\n      return <FaEthereum className=\"text-indigo-400\" />;\n    case 'bsc':\n      return <SiBinance className=\"text-yellow-500\" />;\n    default:\n      return <div className=\"w-6 h-6 rounded-full bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center\">\n        <span className=\"text-xs font-bold\">{chain.charAt(0)}</span>\n      </div>;\n  }\n};\n\ninterface WalletCardProps {\n  name: string;\n  address: string;\n  balance: string;\n  chain: string;\n  onDeposit: () => void;\n  onWithdraw: () => void;\n  onExport: () => void;\n  onEdit: () => void;\n  onArchive: () => void;\n}\n\nconst WalletCard = ({\n  name,\n  address,\n  balance,\n  chain,\n  onDeposit,\n  onWithdraw,\n  onExport,\n  onEdit,\n  onArchive,\n}: WalletCardProps) => {\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Close dropdown when clicking outside\n  useClickAway(dropdownRef, () => {\n    if (isDropdownOpen) setIsDropdownOpen(false);\n  });\n\n  const toggleExpand = () => {\n    setIsExpanded(!isExpanded);\n    if (isDropdownOpen) setIsDropdownOpen(false);\n  };\n\n  const toggleDropdown = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    setIsDropdownOpen(!isDropdownOpen);\n  };\n\n  const handleCopy = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    navigator.clipboard.writeText(address);\n    // Optional: Add toast notification\n  };\n\n  const handleAction = (e: React.MouseEvent, action: () => void) => {\n    e.stopPropagation();\n    action();\n    setIsDropdownOpen(false);\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 10 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -10 }}\n      transition={{ duration: 0.2 }}\n      className={`relative p-5 rounded-xl border shadow-sm bg-white border-gray-200 transition-all duration-300 cursor-pointer overflow-hidden ${isExpanded ? 'ring-2 ring-black' : ''}`}\n      onClick={toggleExpand}\n    >\n      {/* Animated border highlight on hover */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-blue-900/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300\"></div>\n\n      <div className=\"relative z-10\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex items-start space-x-4\">\n            <div className=\"mt-1\">\n              <div className=\"w-10 h-10 rounded-full bg-gradient-to-br from-blue-700 to-blue-900 flex items-center justify-center shadow-lg\">\n                {getChainIcon(chain)}\n              </div>\n            </div>\n            <div>\n              <h4 className=\"font-bold text-black flex items-center\">\n                {name}\n                <span className=\"ml-2 px-2 py-0.5 text-xs bg-gray-200 text-black rounded-full\">\n                  {chain}\n                </span>\n              </h4>\n              <div className=\"flex items-center mt-1\">\n                <span className=\"text-sm text-gray-400 font-mono\">\n                  {truncateAddress(address)}\n                </span>\n                <button\n                  onClick={handleCopy}\n                  className=\"ml-2 p-1 rounded-full text-black hover:bg-gray-100 transition-colors\"\n                  aria-label=\"Copy address\"\n                >\n                  <FiCopy size={14} />\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"text-right\">\n              <p className=\"text-xs font-medium text-gray-700\">BALANCE</p>\n              <p className=\"text-lg font-bold text-black\">\n                ${balance}\n              </p>\n            </div>\n\n            <div className=\"relative z-20\" ref={dropdownRef}>\n              <button\n                onClick={toggleDropdown}\n                className=\"p-1.5 rounded-lg hover:bg-gray-100 text-black hover:text-black transition-colors\"\n                aria-label=\"Wallet actions\"\n              >\n                <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                  <circle cx=\"12\" cy=\"12\" r=\"1\" />\n                  <circle cx=\"12\" cy=\"5\" r=\"1\" />\n                  <circle cx=\"12\" cy=\"19\" r=\"1\" />\n                </svg>\n              </button>\n\n              <AnimatePresence>\n                {isDropdownOpen && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                    animate={{ opacity: 1, y: 0, scale: 1 }}\n                    exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                    transition={{ duration: 0.15, ease: \"easeOut\" }}\n                    className=\"absolute right-0 mt-2 w-56 origin-top-right rounded-xl bg-white border border-gray-200 overflow-hidden z-50\"\n                    style={{\n                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',\n                    }}\n                  >\n                    <div className=\"py-1\">\n                      <button\n                        onClick={(e) => handleAction(e, onDeposit)}\n                        className=\"w-full flex items-center px-4 py-2.5 text-sm text-black hover:bg-gray-100 hover:text-black transition-colors\"\n                      >\n                        <FiDownload className=\"mr-3 text-gray-700\" size={16} />\n                        <span>Deposit</span>\n                      </button>\n                      <button\n                        onClick={(e) => handleAction(e, onWithdraw)}\n                        className=\"w-full flex items-center px-4 py-2.5 text-sm text-black hover:bg-gray-100 hover:text-black transition-colors\"\n                      >\n                        <FiUpload className=\"mr-3 text-gray-700\" size={16} />\n                        <span>Withdraw</span>\n                      </button>\n                      <button\n                        onClick={(e) => handleAction(e, onExport)}\n                        className=\"w-full flex items-center px-4 py-2.5 text-sm text-black hover:bg-gray-100 hover:text-black transition-colors\"\n                      >\n                        <FiDownload className=\"mr-3 text-gray-700\" size={16} />\n                        <span>Export Private Key</span>\n                      </button>\n                      <div className=\"border-t border-gray-200 my-1\"></div>\n                      <button\n                        onClick={(e) => handleAction(e, onEdit)}\n                        className=\"w-full flex items-center px-4 py-2.5 text-sm text-black hover:bg-gray-100 hover:text-black transition-colors\"\n                      >\n                        <FiEdit2 className=\"mr-3 text-gray-700\" size={16} />\n                        <span>Edit Wallet</span>\n                      </button>\n                      <button\n                        onClick={(e) => handleAction(e, onArchive)}\n                        className=\"w-full flex items-center px-4 py-2.5 text-sm text-red-600 hover:bg-red-100 transition-colors\"\n                      >\n                        <FiTrash2 className=\"mr-3\" size={16} />\n                        <span>Archive Wallet</span>\n                      </button>\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n\n            <button\n              className=\"p-1.5 rounded-lg hover:bg-gray-100 text-black hover:text-black transition-colors\"\n              onClick={toggleExpand}\n              aria-label={isExpanded ? 'Collapse' : 'Expand'}\n            >\n              <svg\n                className={`w-5 h-5 transform transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        <AnimatePresence>\n          {isExpanded && (\n            <motion.div\n              initial={{ height: 0, opacity: 0, marginTop: 0 }}\n              animate={{ height: 'auto', opacity: 1, marginTop: '1rem' }}\n              exit={{ height: 0, opacity: 0, marginTop: 0 }}\n              transition={{ duration: 0.2, ease: \"easeInOut\" }}\n              className=\"overflow-hidden\"\n            >\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center justify-between mb-3\">\n                  <h5 className=\"text-sm font-semibold text-gray-700\">ASSETS</h5>\n                  <span className=\"text-xs text-gray-400\">Value</span>\n                </div>\n\n                <div className=\"space-y-3\">\n                  {/* Example asset - in a real app, this would be mapped from wallet assets */}\n                  <div className=\"flex items-center justify-between p-3 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center\">\n                        <FaEthereum className=\"text-black\" size={14} />\n                      </div>\n                      <div>\n                        <div className=\"font-medium text-black\">Ethereum</div>\n                        <div className=\"text-xs text-gray-700\">ETH</div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"font-medium text-black\">$3,450.25</div>\n                      <div className=\"text-xs text-gray-700\">0.5 ETH</div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center justify-between p-3 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center\">\n                        <SiBinance className=\"text-black\" size={14} />\n                      </div>\n                      <div>\n                        <div className=\"font-medium text-black\">USD Coin</div>\n                        <div className=\"text-xs text-gray-700\">USDC</div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"font-medium text-black\">$1,250.75</div>\n                      <div className=\"text-xs text-green-600\">1,250.75 USDC</div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"mt-4 pt-3 border-t border-gray-200\">\n                  <button className=\"w-full py-2 px-4 bg-black hover:bg-gray-900 text-white rounded-lg text-sm font-medium transition-colors flex items-center justify-center space-x-2\">\n                    <span>View All Assets</span>\n                    <svg className=\"w-4 h-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  </button>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default WalletCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,sBAAsB;AACtB,MAAM,eAAe;IACnB,SAAS;IACT,WAAW;IACX,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;AACT;AAEA,MAAM,kBAAkB,CAAC,SAAiB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAC1D,IAAI,CAAC,SAAS,OAAO;IACrB,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,OAAO,GAAG,EAAE,QAAQ,KAAK,CAAC,CAAC,MAAM;AAC9D;AAEA,MAAM,eAAe,CAAC;IACpB,OAAQ,MAAM,WAAW;QACvB,KAAK;YACH,qBAAO,8OAAC,8IAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC/B,KAAK;YACH,qBAAO,8OAAC,8IAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC9B;YACE,qBAAO,8OAAC;gBAAI,WAAU;0BACpB,cAAA,8OAAC;oBAAK,WAAU;8BAAqB,MAAM,MAAM,CAAC;;;;;;;;;;;IAExD;AACF;AAcA,MAAM,aAAa,CAAC,EAClB,IAAI,EACJ,OAAO,EACP,OAAO,EACP,KAAK,EACL,SAAS,EACT,UAAU,EACV,QAAQ,EACR,MAAM,EACN,SAAS,EACO;IAChB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,8LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;QACxB,IAAI,gBAAgB,kBAAkB;IACxC;IAEA,MAAM,eAAe;QACnB,cAAc,CAAC;QACf,IAAI,gBAAgB,kBAAkB;IACxC;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,eAAe;QACjB,kBAAkB,CAAC;IACrB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,eAAe;QACjB,UAAU,SAAS,CAAC,SAAS,CAAC;IAC9B,mCAAmC;IACrC;IAEA,MAAM,eAAe,CAAC,GAAqB;QACzC,EAAE,eAAe;QACjB;QACA,kBAAkB;IACpB;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC3B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAC,6HAA6H,EAAE,aAAa,sBAAsB,IAAI;QAClL,SAAS;;0BAGT,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,aAAa;;;;;;;;;;;kDAGlB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDACX;kEACD,8OAAC;wDAAK,WAAU;kEACb;;;;;;;;;;;;0DAGL,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,gBAAgB;;;;;;kEAEnB,8OAAC;wDACC,SAAS;wDACT,WAAU;wDACV,cAAW;kEAEX,cAAA,8OAAC,8IAAA,CAAA,SAAM;4DAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDAA+B;oDACxC;;;;;;;;;;;;;kDAIN,8OAAC;wCAAI,WAAU;wCAAgB,KAAK;;0DAClC,8OAAC;gDACC,SAAS;gDACT,WAAU;gDACV,cAAW;0DAEX,cAAA,8OAAC;oDAAI,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;;sEACrI,8OAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;sEAC1B,8OAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAI,GAAE;;;;;;sEACzB,8OAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;;;;;;;;;;;;0DAI9B,8OAAC,yLAAA,CAAA,kBAAe;0DACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;wDAAI,OAAO;oDAAK;oDAC1C,SAAS;wDAAE,SAAS;wDAAG,GAAG;wDAAG,OAAO;oDAAE;oDACtC,MAAM;wDAAE,SAAS;wDAAG,GAAG;wDAAI,OAAO;oDAAK;oDACvC,YAAY;wDAAE,UAAU;wDAAM,MAAM;oDAAU;oDAC9C,WAAU;oDACV,OAAO;wDACL,WAAW;oDACb;8DAEA,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,CAAC,IAAM,aAAa,GAAG;gEAChC,WAAU;;kFAEV,8OAAC,8IAAA,CAAA,aAAU;wEAAC,WAAU;wEAAqB,MAAM;;;;;;kFACjD,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEACC,SAAS,CAAC,IAAM,aAAa,GAAG;gEAChC,WAAU;;kFAEV,8OAAC,8IAAA,CAAA,WAAQ;wEAAC,WAAU;wEAAqB,MAAM;;;;;;kFAC/C,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEACC,SAAS,CAAC,IAAM,aAAa,GAAG;gEAChC,WAAU;;kFAEV,8OAAC,8IAAA,CAAA,aAAU;wEAAC,WAAU;wEAAqB,MAAM;;;;;;kFACjD,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEACC,SAAS,CAAC,IAAM,aAAa,GAAG;gEAChC,WAAU;;kFAEV,8OAAC,8IAAA,CAAA,UAAO;wEAAC,WAAU;wEAAqB,MAAM;;;;;;kFAC9C,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEACC,SAAS,CAAC,IAAM,aAAa,GAAG;gEAChC,WAAU;;kFAEV,8OAAC,8IAAA,CAAA,WAAQ;wEAAC,WAAU;wEAAO,MAAM;;;;;;kFACjC,8OAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQlB,8OAAC;wCACC,WAAU;wCACV,SAAS;wCACT,cAAY,aAAa,aAAa;kDAEtC,cAAA,8OAAC;4CACC,WAAW,CAAC,oDAAoD,EAAE,aAAa,eAAe,IAAI;4CAClG,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7E,8OAAC,yLAAA,CAAA,kBAAe;kCACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,QAAQ;gCAAG,SAAS;gCAAG,WAAW;4BAAE;4BAC/C,SAAS;gCAAE,QAAQ;gCAAQ,SAAS;gCAAG,WAAW;4BAAO;4BACzD,MAAM;gCAAE,QAAQ;gCAAG,SAAS;gCAAG,WAAW;4BAAE;4BAC5C,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAY;4BAC/C,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAG1C,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,8IAAA,CAAA,aAAU;oEAAC,WAAU;oEAAa,MAAM;;;;;;;;;;;0EAE3C,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAG3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAyB;;;;;;0EACxC,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAI3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;oEAAC,WAAU;oEAAa,MAAM;;;;;;;;;;;0EAE1C,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAG3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAyB;;;;;;0EACxC,8OAAC;gEAAI,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;kDAK9C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9D,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3F;uCAEe", "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Wallets/ExportModal/index.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FiX, <PERSON>Eye, FiEyeOff, FiCopy, FiDownload, FiShield, FiAlertTriangle } from 'react-icons/fi';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getWalletPrivateKey } from '@/services/bot/wallets';\nimport { toast } from 'react-toastify';\n\ninterface ExportModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  walletAddress: `0x${string}`;\n  walletName: string;\n}\n\nexport default function ExportModal({\n  isOpen,\n  onClose,\n  walletAddress,\n  walletName\n}: ExportModalProps) {\n  const { token } = useAuth();\n  const [step, setStep] = useState<'warning' | 'confirm' | 'export'>('warning');\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [showPrivate<PERSON>ey, setShowPrivateKey] = useState(false);\n  const [showMnemonic, setShowMnemonic] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [exportData, setExportData] = useState<{\n    privateKey: string;\n    mnemonic?: string;\n  } | null>(null);\n  const [agreedToTerms, setAgreedToTerms] = useState(false);\n\n  const handleExport = async () => {\n    if (!token) {\n      toast.error('Please connect your wallet');\n      return;\n    }\n\n    if (password !== confirmPassword) {\n      toast.error('Passwords do not match');\n      return;\n    }\n\n    if (!agreedToTerms) {\n      toast.error('Please agree to the security terms');\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      const result = await getWalletPrivateKey(token, walletAddress);\n      setExportData({\n        privateKey: result.privateKey,\n        mnemonic: result.phrase\n      });\n      setStep('export');\n    } catch (error) {\n      console.error('Error exporting wallet:', error);\n      toast.error('Failed to export wallet. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCopy = (text: string, type: string) => {\n    navigator.clipboard.writeText(text).then(() => {\n      toast.success(`${type} copied to clipboard`);\n    }).catch(() => {\n      toast.error(`Failed to copy ${type}`);\n    });\n  };\n\n  const handleDownload = () => {\n    if (!exportData) return;\n\n    const data = {\n      address: walletAddress,\n      privateKey: exportData.privateKey,\n      mnemonic: exportData.mnemonic,\n      exportedAt: new Date().toISOString(),\n      warning: 'KEEP THIS FILE SECURE - NEVER SHARE YOUR PRIVATE KEY'\n    };\n\n    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `wallet-${walletAddress.slice(0, 8)}-export.json`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n    \n    toast.success('Wallet data downloaded');\n  };\n\n  const handleClose = () => {\n    setStep('warning');\n    setPassword('');\n    setConfirmPassword('');\n    setShowPrivateKey(false);\n    setShowMnemonic(false);\n    setExportData(null);\n    setAgreedToTerms(false);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50\"\n        onClick={handleClose}\n      >\n        <motion.div\n          initial={{ scale: 0.9, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          exit={{ scale: 0.9, opacity: 0 }}\n          className=\"bg-gray-900 rounded-xl border border-red-500/30 w-full max-w-md overflow-hidden shadow-2xl\"\n          onClick={(e) => e.stopPropagation()}\n        >\n          {/* Header */}\n          <div className=\"p-6 border-b border-red-500/20 bg-red-900/20\">\n            <div className=\"flex justify-between items-center\">\n              <div className=\"flex items-center gap-3\">\n                <FiShield className=\"text-red-400\" size={24} />\n                <div>\n                  <h3 className=\"text-xl font-bold text-white\">Export Wallet</h3>\n                  <p className=\"text-sm text-red-300\">{walletName}</p>\n                </div>\n              </div>\n              <button\n                onClick={handleClose}\n                className=\"text-gray-400 hover:text-white transition-colors\"\n              >\n                <FiX size={24} />\n              </button>\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-6\">\n            {step === 'warning' && (\n              <div className=\"space-y-4\">\n                <div className=\"flex items-start gap-3 p-4 bg-red-900/30 border border-red-500/30 rounded-lg\">\n                  <FiAlertTriangle className=\"text-red-400 mt-0.5\" size={20} />\n                  <div>\n                    <h4 className=\"font-semibold text-red-300 mb-2\">Security Warning</h4>\n                    <ul className=\"text-sm text-red-200 space-y-1\">\n                      <li>• Your private key gives full access to your wallet</li>\n                      <li>• Never share it with anyone</li>\n                      <li>• Store it securely offline</li>\n                      <li>• Anyone with your private key can steal your funds</li>\n                    </ul>\n                  </div>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <label className=\"flex items-start gap-3\">\n                    <input\n                      type=\"checkbox\"\n                      checked={agreedToTerms}\n                      onChange={(e) => setAgreedToTerms(e.target.checked)}\n                      className=\"mt-1 accent-red-500\"\n                    />\n                    <span className=\"text-sm text-gray-300\">\n                      I understand the risks and will keep my private key secure\n                    </span>\n                  </label>\n                </div>\n\n                <div className=\"flex gap-3 pt-4\">\n                  <button\n                    onClick={handleClose}\n                    className=\"flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    onClick={() => setStep('confirm')}\n                    disabled={!agreedToTerms}\n                    className=\"flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    Continue\n                  </button>\n                </div>\n              </div>\n            )}\n\n            {step === 'confirm' && (\n              <div className=\"space-y-4\">\n                <div>\n                  <h4 className=\"font-semibold text-white mb-2\">Confirm Export</h4>\n                  <p className=\"text-sm text-gray-400 mb-4\">\n                    Enter a password to encrypt your wallet data (optional but recommended)\n                  </p>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300 mb-1\">\n                      Password (Optional)\n                    </label>\n                    <input\n                      type=\"password\"\n                      value={password}\n                      onChange={(e) => setPassword(e.target.value)}\n                      className=\"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                      placeholder=\"Enter password for encryption\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300 mb-1\">\n                      Confirm Password\n                    </label>\n                    <input\n                      type=\"password\"\n                      value={confirmPassword}\n                      onChange={(e) => setConfirmPassword(e.target.value)}\n                      className=\"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                      placeholder=\"Confirm password\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"flex gap-3 pt-4\">\n                  <button\n                    onClick={() => setStep('warning')}\n                    className=\"flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors\"\n                  >\n                    Back\n                  </button>\n                  <button\n                    onClick={handleExport}\n                    disabled={isLoading || (password && password !== confirmPassword)}\n                    className=\"flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\"\n                  >\n                    {isLoading ? (\n                      <>\n                        <div className=\"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n                        Exporting...\n                      </>\n                    ) : (\n                      'Export Wallet'\n                    )}\n                  </button>\n                </div>\n              </div>\n            )}\n\n            {step === 'export' && exportData && (\n              <div className=\"space-y-4\">\n                <div>\n                  <h4 className=\"font-semibold text-white mb-2\">Wallet Exported</h4>\n                  <p className=\"text-sm text-gray-400 mb-4\">\n                    Your wallet data is displayed below. Copy or download it securely.\n                  </p>\n                </div>\n\n                <div className=\"space-y-4\">\n                  {/* Private Key */}\n                  <div>\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <label className=\"text-sm font-medium text-gray-300\">Private Key</label>\n                      <button\n                        onClick={() => setShowPrivateKey(!showPrivateKey)}\n                        className=\"text-gray-400 hover:text-white transition-colors\"\n                      >\n                        {showPrivateKey ? <FiEyeOff size={16} /> : <FiEye size={16} />}\n                      </button>\n                    </div>\n                    <div className=\"relative\">\n                      <textarea\n                        readOnly\n                        value={showPrivateKey ? exportData.privateKey : '•'.repeat(64)}\n                        className=\"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white font-mono text-xs resize-none focus:outline-none\"\n                        rows={3}\n                      />\n                      <button\n                        onClick={() => handleCopy(exportData.privateKey, 'Private key')}\n                        className=\"absolute top-2 right-2 p-1 text-gray-400 hover:text-white transition-colors\"\n                      >\n                        <FiCopy size={14} />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Mnemonic */}\n                  {exportData.mnemonic && (\n                    <div>\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <label className=\"text-sm font-medium text-gray-300\">Mnemonic Phrase</label>\n                        <button\n                          onClick={() => setShowMnemonic(!showMnemonic)}\n                          className=\"text-gray-400 hover:text-white transition-colors\"\n                        >\n                          {showMnemonic ? <FiEyeOff size={16} /> : <FiEye size={16} />}\n                        </button>\n                      </div>\n                      <div className=\"relative\">\n                        <textarea\n                          readOnly\n                          value={showMnemonic ? exportData.mnemonic : '•'.repeat(exportData.mnemonic.length)}\n                          className=\"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white font-mono text-xs resize-none focus:outline-none\"\n                          rows={3}\n                        />\n                        <button\n                          onClick={() => handleCopy(exportData.mnemonic!, 'Mnemonic phrase')}\n                          className=\"absolute top-2 right-2 p-1 text-gray-400 hover:text-white transition-colors\"\n                        >\n                          <FiCopy size={14} />\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"flex gap-3 pt-4\">\n                  <button\n                    onClick={handleDownload}\n                    className=\"flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center justify-center gap-2\"\n                  >\n                    <FiDownload size={16} />\n                    Download\n                  </button>\n                  <button\n                    onClick={handleClose}\n                    className=\"flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors\"\n                  >\n                    Done\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAgBe,SAAS,YAAY,EAClC,MAAM,EACN,OAAO,EACP,aAAa,EACb,UAAU,EACO;IACjB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACxB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGjC;IACV,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO;YACV,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,aAAa,iBAAiB;YAChC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,eAAe;YAClB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;YAChD,cAAc;gBACZ,YAAY,OAAO,UAAU;gBAC7B,UAAU,OAAO,MAAM;YACzB;YACA,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa,CAAC,MAAc;QAChC,UAAU,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC;YACvC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,KAAK,oBAAoB,CAAC;QAC7C,GAAG,KAAK,CAAC;YACP,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,MAAM;QACtC;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,YAAY;QAEjB,MAAM,OAAO;YACX,SAAS;YACT,YAAY,WAAW,UAAU;YACjC,UAAU,WAAW,QAAQ;YAC7B,YAAY,IAAI,OAAO,WAAW;YAClC,SAAS;QACX;QAEA,MAAM,OAAO,IAAI,KAAK;YAAC,KAAK,SAAS,CAAC,MAAM,MAAM;SAAG,EAAE;YAAE,MAAM;QAAmB;QAClF,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,OAAO,EAAE,cAAc,KAAK,CAAC,GAAG,GAAG,YAAY,CAAC;QAC9D,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;QAEpB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,cAAc;QAClB,QAAQ;QACR,YAAY;QACZ,mBAAmB;QACnB,kBAAkB;QAClB,gBAAgB;QAChB,cAAc;QACd,iBAAiB;QACjB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;sBAET,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAC/B,WAAU;gBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8IAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAe,MAAM;;;;;;sDACzC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,8IAAA,CAAA,MAAG;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;kCAMjB,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,2BACR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8IAAA,CAAA,kBAAe;gDAAC,WAAU;gDAAsB,MAAM;;;;;;0DACvD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAkC;;;;;;kEAChD,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;kDAKV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,OAAO;oDAClD,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;;kDAM5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,QAAQ;gDACvB,UAAU,CAAC;gDACX,WAAU;0DACX;;;;;;;;;;;;;;;;;;4BAON,SAAS,2BACR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAgC;;;;;;0DAC9C,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAK5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC3C,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDAClD,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,QAAQ;gDACvB,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS;gDACT,UAAU,aAAc,YAAY,aAAa;gDACjD,WAAU;0DAET,0BACC;;sEACE,8OAAC;4DAAI,WAAU;;;;;;wDAAkF;;mEAInG;;;;;;;;;;;;;;;;;;4BAOT,SAAS,YAAY,4BACpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAgC;;;;;;0DAC9C,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAK5C,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEACC,SAAS,IAAM,kBAAkB,CAAC;gEAClC,WAAU;0EAET,+BAAiB,8OAAC,8IAAA,CAAA,WAAQ;oEAAC,MAAM;;;;;yFAAS,8OAAC,8IAAA,CAAA,QAAK;oEAAC,MAAM;;;;;;;;;;;;;;;;;kEAG5D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,QAAQ;gEACR,OAAO,iBAAiB,WAAW,UAAU,GAAG,IAAI,MAAM,CAAC;gEAC3D,WAAU;gEACV,MAAM;;;;;;0EAER,8OAAC;gEACC,SAAS,IAAM,WAAW,WAAW,UAAU,EAAE;gEACjD,WAAU;0EAEV,cAAA,8OAAC,8IAAA,CAAA,SAAM;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;4CAMnB,WAAW,QAAQ,kBAClB,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEACC,SAAS,IAAM,gBAAgB,CAAC;gEAChC,WAAU;0EAET,6BAAe,8OAAC,8IAAA,CAAA,WAAQ;oEAAC,MAAM;;;;;yFAAS,8OAAC,8IAAA,CAAA,QAAK;oEAAC,MAAM;;;;;;;;;;;;;;;;;kEAG1D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,QAAQ;gEACR,OAAO,eAAe,WAAW,QAAQ,GAAG,IAAI,MAAM,CAAC,WAAW,QAAQ,CAAC,MAAM;gEACjF,WAAU;gEACV,MAAM;;;;;;0EAER,8OAAC;gEACC,SAAS,IAAM,WAAW,WAAW,QAAQ,EAAG;gEAChD,WAAU;0EAEV,cAAA,8OAAC,8IAAA,CAAA,SAAM;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,8OAAC,8IAAA,CAAA,aAAU;wDAAC,MAAM;;;;;;oDAAM;;;;;;;0DAG1B,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 1583, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/utils/web3/config.ts"], "sourcesContent": ["import { createPublicClient, http } from 'viem'\r\nimport { avalanche, base, unichain, berachain, sonic } from 'viem/chains'\r\n\r\nconst systemClient = (chain: any) => {\r\n    return createPublicClient({\r\n        chain,\r\n        transport: http()\r\n    })\r\n}\r\n\r\nexport const avalancheClient = systemClient(avalanche)\r\nexport const baseClient = systemClient(base)\r\nexport const unichainClient = systemClient(unichain)\r\nexport const berachainClient = systemClient(berachain)\r\nexport const sonicClient = systemClient(sonic)\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAEA,MAAM,eAAe,CAAC;IAClB,OAAO,CAAA,GAAA,6JAAA,CAAA,qBAAkB,AAAD,EAAE;QACtB;QACA,WAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD;IAClB;AACJ;AAEO,MAAM,kBAAkB,aAAa,kKAAA,CAAA,YAAS;AAC9C,MAAM,aAAa,aAAa,6JAAA,CAAA,OAAI;AACpC,MAAM,iBAAiB,aAAa,iKAAA,CAAA,WAAQ;AAC5C,MAAM,kBAAkB,aAAa,kKAAA,CAAA,YAAS;AAC9C,MAAM,cAAc,aAAa,8JAAA,CAAA,QAAK", "debugId": null}}, {"offset": {"line": 1616, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/services/viem/index.ts"], "sourcesContent": ["// import { ethers } from \"ethers\";\r\nimport { avalancheClient, baseClient, unichainClient, berachainClient, sonicClient } from \"@/utils/web3/config\";\r\n\r\nexport const getBalanceForEachChain = async (address: `0x${string}`) => {\r\n    const clients = [\r\n        { name: \"Avalanche\", client: avalancheClient },\r\n        { name: \"Base\", client: baseClient },\r\n        { name: \"Unichain\", client: unichainClient },\r\n        { name: \"<PERSON><PERSON><PERSON><PERSON>\", client: berachainClient },\r\n        { name: \"<PERSON>\", client: sonicClient }\r\n    ];\r\n\r\n    const balances = clients.map(async ({ name, client }) => {\r\n        try {\r\n            const balance = await client.getBalance({ address });\r\n            return { name, balance };\r\n        } catch (error) {\r\n            console.error(`Error fetching balance for ${name}:`, error);\r\n            return { name, balance: BigInt(0) };\r\n        }\r\n    });\r\n\r\n    return Promise.all(balances);\r\n}"], "names": [], "mappings": "AAAA,mCAAmC;;;;AACnC;;AAEO,MAAM,yBAAyB,OAAO;IACzC,MAAM,UAAU;QACZ;YAAE,MAAM;YAAa,QAAQ,8HAAA,CAAA,kBAAe;QAAC;QAC7C;YAAE,MAAM;YAAQ,QAAQ,8HAAA,CAAA,aAAU;QAAC;QACnC;YAAE,MAAM;YAAY,QAAQ,8HAAA,CAAA,iBAAc;QAAC;QAC3C;YAAE,MAAM;YAAa,QAAQ,8HAAA,CAAA,kBAAe;QAAC;QAC7C;YAAE,MAAM;YAAS,QAAQ,8HAAA,CAAA,cAAW;QAAC;KACxC;IAED,MAAM,WAAW,QAAQ,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE;QAChD,IAAI;YACA,MAAM,UAAU,MAAM,OAAO,UAAU,CAAC;gBAAE;YAAQ;YAClD,OAAO;gBAAE;gBAAM;YAAQ;QAC3B,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC,EAAE;YACrD,OAAO;gBAAE;gBAAM,SAAS,OAAO;YAAG;QACtC;IACJ;IAEA,OAAO,QAAQ,GAAG,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 1670, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/app/terminal/wallets/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useAuth } from \"@/contexts/AuthContext\";\nimport { useUserData } from \"@/hooks/useUserData\";\nimport { useRouter } from \"next/navigation\";\nimport { useEffect, useState, useCallback, useMemo } from \"react\";\nimport { <PERSON>Plus, FiFrown as Frown, FiUpload as Upload, FiSearch, FiCopy, FiExternalLink, FiTrash2, FiEdit2, FiCheck, FiX, FiDownload } from \"react-icons/fi\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport WalletCard from \"@/components/Terminal/Wallets/WalletCard\";\nimport WalletSelector from \"@/components/Terminal/Wallets/Dropdown\";\nimport ExportModal from \"@/components/Terminal/Wallets/ExportModal\";\nimport { Wallet, createWallet, getWallets, importWallet, setPrimaryWallet, getWalletPrivateKey, getWalletBalance, exportWallet, deleteWallet, updateWalletName } from \"@/services/bot\";\nimport { getBalanceForEach<PERSON>hain } from \"@/services/viem\";\nimport { chains } from \"@/utils/data\";\nimport { useWeb3 } from \"@/contexts/Web3Context\";\n\n// Extended Wallet type with UI-specific properties\ninterface UIExtendedWallet extends Wallet {\n  id: string; // Use address as ID\n  name: string;\n  chain: string;\n  balance: number;\n  isPrimary: boolean;\n  isTrading: boolean;\n  assets?: Array<{\n    symbol: string;\n    balance: string;\n    usdValue: string;\n  }>;\n  nativeBalances?: Array<{\n    name: string;\n    balance: bigint;\n  }>;\n}\n\n// Helper function to format currency\nconst formatCurrency = (value: number): string => {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(value);\n};\n\n// Animation variants\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  show: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.1,\n    },\n  },\n};\n\nconst itemVariants = {\n  hidden: { opacity: 0, y: 20 },\n  show: { opacity: 1, y: 0, transition: { duration: 0.3 } },\n};\n\nexport default function Wallets() {\n  const { token } = useAuth();\n  const { data: userData } = useUserData(token);\n  const { selectedChain } = useWeb3();\n  const router = useRouter();\n  const [redirecting, setRedirecting] = useState(false);\n  // State for wallets and UI\n  const [wallets, setWallets] = useState<UIExtendedWallet[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showImportModal, setShowImportModal] = useState(false);\n  const [importPrivateKey, setImportPrivateKey] = useState('');\n  const [importWalletName, setImportWalletName] = useState('');\n  const [isImporting, setIsImporting] = useState(false);\n  const [isCreating, setIsCreating] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);\n  const [walletName, setWalletName] = useState('');\n  const [privateKey, setPrivateKey] = useState('');\n  const [expandedWallet, setExpandedWallet] = useState<string | null>(null);\n  const [exportedKey, setExportedKey] = useState<string | null>(null);\n  const [showExportModal, setShowExportModal] = useState(false);\n  const [exportWalletId, setExportWalletId] = useState<string | null>(null);\n  const [exportWalletAddress, setExportWalletAddress] = useState<`0x${string}` | null>(null);\n  const [exportWalletName, setExportWalletName] = useState<string>('');\n\n  // Fetch wallets and enrich with native balances\n  const fetchWallets = useCallback(async () => {\n    if (!token) return;\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await getWallets(token);\n      if (Array.isArray(response)) {\n        // Enrich wallets with native balances from viem\n        const walletsWithUIProps: UIExtendedWallet[] = await Promise.all(\n          response.map(async (wallet: any) => {\n            let nativeBalances: Array<{ name: string; balance: bigint }> = [];\n            try {\n              nativeBalances = await getBalanceForEachChain(wallet.address);\n            } catch (e) {\n              nativeBalances = [];\n            }\n            return {\n              ...wallet,\n              id: wallet.address,\n              name: `Wallet ${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`,\n              chain: 'Multi',\n              balance: 0, // Will be updated by getWalletBalance if needed\n              isPrimary: wallet.isPrimary || false,\n              isTrading: true,\n              assets: [],\n              nativeBalances,\n            };\n          })\n        );\n        setWallets(walletsWithUIProps);\n      } else if (response && typeof response === 'object' && 'error' in response) {\n        const errorResponse = response as { error: string };\n        throw new Error(errorResponse.error);\n      } else {\n        throw new Error('Invalid response from server');\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to load wallets';\n      setError(`Failed to load wallets: ${errorMessage}`);\n    } finally {\n      setLoading(false);\n    }\n  }, [token]);\n\n  // Initial fetch\n  useEffect(() => {\n    if (token) {\n      fetchWallets();\n    }\n  }, [token, fetchWallets]);\n\n  // Handle redirect if no token\n  useEffect(() => {\n    if (!token && !redirecting) {\n      setRedirecting(true);\n      router.replace('/terminal/error');\n    }\n  }, [token, redirecting, router]);\n\n  // Filter wallets based on search query\n  const filteredWallets = useMemo(() => {\n    if (!searchQuery.trim()) return wallets;\n\n    const query = searchQuery.toLowerCase().trim();\n    return wallets.filter(wallet =>\n      wallet.address.toLowerCase().includes(query) ||\n      wallet.name.toLowerCase().includes(query)\n    );\n  }, [wallets, searchQuery]);\n\n  // Handle wallet creation\n  const handleCreateWallet = async () => {\n    if (!token) return;\n\n    setIsCreating(true);\n    try {\n      const newWallet = await createWallet(token);\n      if (newWallet && 'address' in newWallet) {\n        await fetchWallets(); // Refresh the list\n        setShowCreateModal(false);\n        // TODO: Show success toast\n      } else {\n        throw new Error('Invalid wallet creation response');\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n      console.error('Failed to create wallet:', errorMessage);\n      setError(`Failed to create wallet: ${errorMessage}`);\n    } finally {\n      setIsCreating(false);\n    }\n  };\n\n  // Handle wallet import\n  const handleImportWallet = async () => {\n    if (!token || !privateKey) return;\n\n    setIsSubmitting(true);\n    try {\n      const result = await importWallet(token, { privateKey });\n      if (result && 'address' in result) {\n        await fetchWallets(); // Refresh the list\n        setShowImportModal(false);\n        setPrivateKey('');\n        setWalletName('');\n        // TODO: Show success toast\n      } else {\n        throw new Error('Invalid import response');\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Invalid private key';\n      console.error('Failed to import wallet:', errorMessage);\n      setError(`Failed to import wallet: ${errorMessage}`);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Handle set primary wallet\n  const handleSetPrimary = async (walletAddress: `0x${string}`) => {\n    if (!token) return;\n\n    try {\n      await setPrimaryWallet(token, walletAddress);\n      await fetchWallets(); // Refresh the list\n      // TODO: Show success toast\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n      console.error('Failed to set primary wallet:', errorMessage);\n      setError(`Failed to set primary wallet: ${errorMessage}`);\n    }\n  };\n\n  // Handle copy to clipboard\n  const handleCopyAddress = (address: string) => {\n    navigator.clipboard.writeText(address).catch(err => {\n      console.error('Failed to copy address:', err);\n      setError('Failed to copy address to clipboard');\n    });\n    setCopiedAddress(address);\n    setTimeout(() => setCopiedAddress(null), 2000);\n  };\n\n  // Handle view on explorer\n  const handleViewOnExplorer = (address: string) => {\n    try {\n      const explorerUrl = selectedChain?.scanUrl || 'https://etherscan.io';\n      const url = `${explorerUrl}/address/${address}`;\n\n      window.open(url, '_blank', 'noopener,noreferrer');\n    } catch (err) {\n      console.error('Failed to open explorer:', err);\n      setError('Failed to open blockchain explorer');\n    }\n  };\n\n  // Handle wallet archive\n  const handleArchive = async (walletId: string) => {\n    if (!token) return;\n\n    try {\n      // TODO: Implement archive wallet API call when available\n      console.log('Archive wallet:', walletId);\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Optimistic update\n      setWallets(prevWallets =>\n        prevWallets.filter(wallet => wallet.id !== walletId)\n      );\n\n      // TODO: Show success toast when API is implemented\n      console.log('Wallet archived successfully');\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to archive wallet';\n      console.error('Failed to archive wallet:', errorMessage);\n      setError(`Failed to archive wallet: ${errorMessage}`);\n      // Re-fetch to revert optimistic update on error\n      fetchWallets();\n    }\n  };\n\n  // Handle edit wallet name\n  const handleEditName = (walletId: string, newName: string) => {\n    // Optimistic update\n    setWallets(prevWallets =>\n      prevWallets.map(wallet =>\n        wallet.id === walletId ? { ...wallet, name: newName } : wallet\n      )\n    );\n\n    // TODO: Implement API call to update wallet name when available\n    console.log(`Updating wallet ${walletId} name to:`, newName);\n  };\n\n  // Handle wallet export\n  const handleExportWallet = async (walletId: string) => {\n    if (!token) return;\n\n    try {\n      const wallet = wallets.find(w => w.id === walletId);\n      if (wallet) {\n        setExportWalletAddress(wallet.address);\n        setExportWalletName(wallet.name);\n        setShowExportModal(true);\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to export wallet';\n      console.error('Failed to export wallet:', errorMessage);\n      setError(`Failed to export wallet: ${errorMessage}`);\n    }\n  };\n\n  // Handler for deposit action\n  const handleDeposit = (walletId: string) => {\n    const wallet = wallets.find(w => w.id === walletId);\n    if (wallet) {\n      console.log(`Deposit to ${wallet.name}`);\n      // TODO: Implement deposit flow\n    }\n  };\n\n  // Handler for withdraw action\n  const handleWithdraw = (walletId: string) => {\n    const wallet = wallets.find(w => w.id === walletId);\n    if (wallet) {\n      console.log(`Withdraw from ${wallet.name}`);\n      // TODO: Implement withdraw flow\n    }\n  };\n\n  // Handler for edit action\n  const handleEdit = (walletId: string) => {\n    const wallet = wallets.find(w => w.id === walletId);\n    if (wallet) {\n      const newName = prompt('Enter new wallet name:', wallet.name);\n      if (newName && newName !== wallet.name) {\n        handleEditName(walletId, newName);\n      }\n    }\n  };\n\n  // Calculate total balance (regular function, not a hook)\n  const calculateTotalBalance = (wallets: UIExtendedWallet[]): number => {\n    let total = 0;\n    wallets.forEach(wallet => {\n      if (wallet.nativeBalances) {\n        wallet.nativeBalances.forEach(chainBal => {\n          // For demo, just sum as ETH (should convert to USD with price API)\n          total += Number(chainBal.balance) / 1e18;\n        });\n      }\n    });\n    return total;\n  };\n\n  // Memoize the total balance calculation\n  const totalBalance = calculateTotalBalance(wallets);\n\n  // Create handlers object\n  const walletHandlers = {\n    handleDeposit,\n    handleWithdraw,\n    handleExport: handleExportWallet,\n    handleEdit,\n    handleArchive,\n    handleImport: handleImportWallet,\n    handleCopyAddress,\n    handleViewOnExplorer,\n    handleSetPrimary\n  };\n\n  // --- UI ---\n  return (\n    <div className=\"min-h-[calc(100vh-80px)] bg-black p-4 md:p-8\">\n      {/* Header */}\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-8\">\n          <div>\n            <h1 className=\"text-3xl md:text-4xl font-bold text-black\">\n              Wallets\n            </h1>\n            <p className=\"mt-2 text-gray-700\">\n              Manage your cryptocurrency wallets and assets\n            </p>\n          </div>\n\n          <div className=\"mt-4 md:mt-0 flex space-x-3\">\n            <button\n              onClick={() => setShowImportModal(true)}\n              className=\"flex items-center px-4 py-2.5 bg-black text-white rounded-lg transition-colors border border-gray-200\"\n            >\n              <Upload className=\"mr-2\" size={16} />\n              <span>Import</span>\n            </button>\n            <button\n              onClick={() => setShowCreateModal(true)}\n              className=\"flex items-center px-4 py-2.5 bg-black text-white rounded-lg transition-colors border border-gray-200\"\n            >\n              <FiPlus className=\"mr-2\" size={16} />\n              <span>New Wallet</span>\n            </button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8\">\n          <div className=\"bg-white border border-gray-200 rounded-xl p-5 shadow-sm\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-700\">Total Balance (ETH)</p>\n                <p className=\"text-2xl font-bold mt-1 text-black\">\n                  {formatCurrency(totalBalance)}\n                </p>\n              </div>\n              <div className=\"p-3 rounded-full bg-gray-100 text-black\">\n                <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white border border-gray-200 rounded-xl p-5 shadow-sm\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-700\">Total Wallets</p>\n                <p className=\"text-2xl font-bold mt-1 text-black\">\n                  {wallets.length}\n                </p>\n              </div>\n              <div className=\"p-3 rounded-full bg-gray-100 text-black\">\n                <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z\" />\n                </svg>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white border border-gray-200 rounded-xl p-5 shadow-sm\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-700\">Active Chains</p>\n                <p className=\"text-2xl font-bold mt-1 text-black\">\n                  {new Set(wallets.flatMap(w => (w.nativeBalances || []).map(b => b.name))).size}\n                </p>\n              </div>\n              <div className=\"p-3 rounded-full bg-gray-100 text-black\">\n                <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Search and Filter */}\n        <div className=\"mb-6\">\n          <div className=\"relative max-w-md\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <FiSearch className=\"h-5 w-5 text-gray-700\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search wallets by name or address...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2.5 bg-white border border-gray-200 rounded-lg text-black placeholder-gray-700 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent\"\n            />\n          </div>\n        </div>\n\n        {/* Wallets List */}\n        <div className=\"space-y-4 z-10\">\n          {loading ? (\n            // Loading skeleton\n            <div className=\"space-y-4\">\n              {[1, 2, 3].map((i) => (\n                <div key={i} className=\"h-24 bg-gray-100 rounded-xl animate-pulse\"></div>\n              ))}\n            </div>\n          ) : filteredWallets.length > 0 ? (\n            // Wallets grid\n            <motion.div\n              variants={containerVariants}\n              initial=\"hidden\"\n              animate=\"show\"\n              className=\"grid grid-cols-1 gap-4 z-10\"\n            >\n              <AnimatePresence>\n                {filteredWallets.map((wallet) => (\n                  <motion.div key={wallet.id} variants={itemVariants}>\n                    <WalletCard\n                      name={wallet.name}\n                      address={wallet.address}\n                      balance={wallet.nativeBalances?.map(b => `${b.name}: ${(Number(b.balance) / 1e18).toFixed(4)}`).join(' | ') || '0'}\n                      chain={wallet.chain}\n                      onDeposit={() => handleDeposit(wallet.id)}\n                      onWithdraw={() => handleWithdraw(wallet.id)}\n                      onExport={() => handleExportWallet(wallet.id)}\n                      onEdit={() => handleEdit(wallet.id)}\n                      onArchive={() => handleArchive(wallet.id)}\n                    />\n                    {/* Micro-interaction: Expand for details */}\n                    {expandedWallet === wallet.id && (\n                      <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: 10 }} className=\"p-4 bg-gray-100 rounded-xl mt-2 border border-gray-200\">\n                        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n                          <div>\n                            <div className=\"font-semibold text-gray-700 mb-2\">Native Balances</div>\n                            <ul className=\"text-sm text-black/80\">\n                              {wallet.nativeBalances?.map((b, i) => (\n                                <li key={i}>{b.name}: {(Number(b.balance) / 1e18).toFixed(4)}</li>\n                              ))}\n                            </ul>\n                          </div>\n                          <div>\n                            <button onClick={() => handleCopyAddress(wallet.address)} className=\"mr-2 px-3 py-1 bg-gray-100 hover:bg-gray-200 text-black rounded transition-colors\">{copiedAddress === wallet.address ? <FiCheck /> : <FiCopy />} Copy Address</button>\n                            <button onClick={() => handleViewOnExplorer(wallet.address)} className=\"px-3 py-1 bg-gray-100 hover:bg-gray-200 text-black rounded transition-colors\"><FiExternalLink /> View on Explorer</button>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </motion.div>\n                ))}\n              </AnimatePresence>\n            </motion.div>\n          ) : (\n            // Empty state\n            <div className=\"text-center py-16 bg-gray-100 rounded-xl border-2 border-dashed border-gray-200\">\n              <Frown className=\"mx-auto h-12 w-12 text-gray-700\" />\n              <h3 className=\"mt-2 text-lg font-medium text-gray-700\">No wallets found</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                {searchQuery ? 'Try a different search term' : 'Get started by creating a new wallet'}\n              </p>\n              <div className=\"mt-6\">\n                <button\n                  onClick={handleCreateWallet}\n                  disabled={isCreating}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  <FiPlus className=\"-ml-1 mr-2 h-5 w-5\" />\n                  New Wallet\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Import Wallet Modal */}\n      {showImportModal && (\n        <div className=\"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl\">\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"text-xl font-bold text-black\">Import Wallet</h3>\n                <button\n                  onClick={() => setShowImportModal(false)}\n                  className=\"text-gray-700 hover:text-white\"\n                >\n                  <FiX size={24} />\n                </button>\n              </div>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <label htmlFor=\"wallet-name\" className=\"block text-sm font-medium text-black mb-1\">\n                    Wallet Name (Optional)\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"wallet-name\"\n                    value={walletName}\n                    onChange={(e) => setWalletName(e.target.value)}\n                    placeholder=\"e.g. My Imported Wallet\"\n                    className=\"w-full px-3 py-2 bg-gray-100 border border-gray-200 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent\"\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"private-key\" className=\"block text-sm font-medium text-black mb-1\">\n                    Private Key\n                  </label>\n                  <textarea\n                    id=\"private-key\"\n                    rows={3}\n                    value={privateKey}\n                    onChange={(e) => setPrivateKey(e.target.value)}\n                    placeholder=\"Enter your private key...\"\n                    className=\"w-full px-3 py-2 bg-gray-100 border border-gray-200 rounded-md text-black font-mono text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent\"\n                  />\n                  <p className=\"mt-1 text-xs text-gray-500\">\n                    Your private key is encrypted and never leaves your device.\n                  </p>\n                </div>\n\n                <div className=\"pt-2\">\n                  <button\n                    onClick={handleImportWallet}\n                    disabled={!privateKey || isSubmitting}\n                    className=\"w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    {isSubmitting ? 'Importing...' : 'Import Wallet'}\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Create Wallet Modal */}\n      {showCreateModal && (\n        <div className=\"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl\">\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"text-xl font-bold text-black\">Create New Wallet</h3>\n                <button onClick={() => setShowCreateModal(false)} className=\"text-gray-700 hover:text-white\">\n                  <FiX size={24} />\n                </button>\n              </div>\n              <div className=\"space-y-4\">\n                <p className=\"text-gray-700\">A new wallet will be generated and securely added to your account.</p>\n                <div className=\"pt-2\">\n                  <button onClick={handleCreateWallet} disabled={isCreating} className=\"w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed\">\n                    {isCreating ? 'Creating...' : 'Create Wallet'}\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Export Wallet Modal */}\n      {showExportModal && (\n        <div className=\"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl\">\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"text-xl font-bold text-black\">Export Private Key</h3>\n                <button onClick={() => setShowExportModal(false)} className=\"text-gray-700 hover:text-white\">\n                  <FiX size={24} />\n                </button>\n              </div>\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-black mb-1\">Private Key</label>\n                  <textarea readOnly value={exportedKey || ''} className=\"w-full px-3 py-2 bg-gray-100 border border-gray-200 rounded-md text-black font-mono text-sm focus:outline-none\" />\n                  <p className=\"mt-1 text-xs text-gray-500\">Keep your private key safe. Never share it with anyone.</p>\n                </div>\n                <div className=\"pt-2 flex justify-end\">\n                  <button onClick={() => setShowExportModal(false)} className=\"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-black rounded-md\">Close</button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Export Modal */}\n      {showExportModal && exportWalletAddress && (\n        <ExportModal\n          isOpen={showExportModal}\n          onClose={() => {\n            setShowExportModal(false);\n            setExportWalletAddress(null);\n            setExportWalletName('');\n          }}\n          walletAddress={exportWalletAddress}\n          walletName={exportWalletName}\n        />\n      )}\n\n      {/* Error Toast */}\n      {error && (\n        <div className=\"fixed bottom-4 right-4 bg-red-700 text-white px-4 py-2 rounded shadow-lg z-50 animate-pulse\">\n          {error}\n          <button className=\"ml-2\" onClick={() => setError(null)}><FiX /></button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AAAA;AACA;AAEA;AAdA;;;;;;;;;;;;;AAmCA,qCAAqC;AACrC,MAAM,iBAAiB,CAAC;IACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEA,qBAAqB;AACrB,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,MAAM;QACJ,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG;QAAG,YAAY;YAAE,UAAU;QAAI;IAAE;AAC1D;AAEe,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACxB,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;IACvC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,2BAA2B;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACrF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,gDAAgD;IAChD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,OAAO;QACZ,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD,EAAE;YAClC,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,gDAAgD;gBAChD,MAAM,qBAAyC,MAAM,QAAQ,GAAG,CAC9D,SAAS,GAAG,CAAC,OAAO;oBAClB,IAAI,iBAA2D,EAAE;oBACjE,IAAI;wBACF,iBAAiB,MAAM,CAAA,GAAA,gIAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,OAAO;oBAC9D,EAAE,OAAO,GAAG;wBACV,iBAAiB,EAAE;oBACrB;oBACA,OAAO;wBACL,GAAG,MAAM;wBACT,IAAI,OAAO,OAAO;wBAClB,MAAM,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI;wBAC1E,OAAO;wBACP,SAAS;wBACT,WAAW,OAAO,SAAS,IAAI;wBAC/B,WAAW;wBACX,QAAQ,EAAE;wBACV;oBACF;gBACF;gBAEF,WAAW;YACb,OAAO,IAAI,YAAY,OAAO,aAAa,YAAY,WAAW,UAAU;gBAC1E,MAAM,gBAAgB;gBACtB,MAAM,IAAI,MAAM,cAAc,KAAK;YACrC,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS,CAAC,wBAAwB,EAAE,cAAc;QACpD,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAM;IAEV,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT;QACF;IACF,GAAG;QAAC;QAAO;KAAa;IAExB,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,CAAC,aAAa;YAC1B,eAAe;YACf,OAAO,OAAO,CAAC;QACjB;IACF,GAAG;QAAC;QAAO;QAAa;KAAO;IAE/B,uCAAuC;IACvC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,YAAY,IAAI,IAAI,OAAO;QAEhC,MAAM,QAAQ,YAAY,WAAW,GAAG,IAAI;QAC5C,OAAO,QAAQ,MAAM,CAAC,CAAA,SACpB,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,UACtC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;IAEvC,GAAG;QAAC;QAAS;KAAY;IAEzB,yBAAyB;IACzB,MAAM,qBAAqB;QACzB,IAAI,CAAC,OAAO;QAEZ,cAAc;QACd,IAAI;YACF,MAAM,YAAY,MAAM,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE;YACrC,IAAI,aAAa,aAAa,WAAW;gBACvC,MAAM,gBAAgB,mBAAmB;gBACzC,mBAAmB;YACnB,2BAA2B;YAC7B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,CAAC,yBAAyB,EAAE,cAAc;QACrD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB;QACzB,IAAI,CAAC,SAAS,CAAC,YAAY;QAE3B,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAAE;YAAW;YACtD,IAAI,UAAU,aAAa,QAAQ;gBACjC,MAAM,gBAAgB,mBAAmB;gBACzC,mBAAmB;gBACnB,cAAc;gBACd,cAAc;YACd,2BAA2B;YAC7B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,CAAC,yBAAyB,EAAE,cAAc;QACrD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,4BAA4B;IAC5B,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,MAAM,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;YAC9B,MAAM,gBAAgB,mBAAmB;QACzC,2BAA2B;QAC7B,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS,CAAC,8BAA8B,EAAE,cAAc;QAC1D;IACF;IAEA,2BAA2B;IAC3B,MAAM,oBAAoB,CAAC;QACzB,UAAU,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,CAAC,CAAA;YAC3C,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;QACX;QACA,iBAAiB;QACjB,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC5B,IAAI;YACF,MAAM,cAAc,eAAe,WAAW;YAC9C,MAAM,MAAM,GAAG,YAAY,SAAS,EAAE,SAAS;YAE/C,OAAO,IAAI,CAAC,KAAK,UAAU;QAC7B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX;IACF;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,yDAAyD;YACzD,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,oBAAoB;YACpB,WAAW,CAAA,cACT,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;YAG7C,mDAAmD;YACnD,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,CAAC,0BAA0B,EAAE,cAAc;YACpD,gDAAgD;YAChD;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB,CAAC,UAAkB;QACxC,oBAAoB;QACpB,WAAW,CAAA,cACT,YAAY,GAAG,CAAC,CAAA,SACd,OAAO,EAAE,KAAK,WAAW;oBAAE,GAAG,MAAM;oBAAE,MAAM;gBAAQ,IAAI;QAI5D,gEAAgE;QAChE,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,SAAS,CAAC,EAAE;IACtD;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC1C,IAAI,QAAQ;gBACV,uBAAuB,OAAO,OAAO;gBACrC,oBAAoB,OAAO,IAAI;gBAC/B,mBAAmB;YACrB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,CAAC,yBAAyB,EAAE,cAAc;QACrD;IACF;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,QAAQ;YACV,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,IAAI,EAAE;QACvC,+BAA+B;QACjC;IACF;IAEA,8BAA8B;IAC9B,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,QAAQ;YACV,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,IAAI,EAAE;QAC1C,gCAAgC;QAClC;IACF;IAEA,0BAA0B;IAC1B,MAAM,aAAa,CAAC;QAClB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,QAAQ;YACV,MAAM,UAAU,OAAO,0BAA0B,OAAO,IAAI;YAC5D,IAAI,WAAW,YAAY,OAAO,IAAI,EAAE;gBACtC,eAAe,UAAU;YAC3B;QACF;IACF;IAEA,yDAAyD;IACzD,MAAM,wBAAwB,CAAC;QAC7B,IAAI,QAAQ;QACZ,QAAQ,OAAO,CAAC,CAAA;YACd,IAAI,OAAO,cAAc,EAAE;gBACzB,OAAO,cAAc,CAAC,OAAO,CAAC,CAAA;oBAC5B,mEAAmE;oBACnE,SAAS,OAAO,SAAS,OAAO,IAAI;gBACtC;YACF;QACF;QACA,OAAO;IACT;IAEA,wCAAwC;IACxC,MAAM,eAAe,sBAAsB;IAE3C,yBAAyB;IACzB,MAAM,iBAAiB;QACrB;QACA;QACA,cAAc;QACd;QACA;QACA,cAAc;QACd;QACA;QACA;IACF;IAEA,aAAa;IACb,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAG1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAKpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;0DAEV,8OAAC,8IAAA,CAAA,WAAM;gDAAC,WAAU;gDAAO,MAAM;;;;;;0DAC/B,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;0DAEV,8OAAC,8IAAA,CAAA,SAAM;gDAAC,WAAU;gDAAO,MAAM;;;;;;0DAC/B,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAMZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,eAAe;;;;;;;;;;;;sDAGpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,QAAQ,MAAM;;;;;;;;;;;;sDAGnB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,IAAI,IAAI,QAAQ,OAAO,CAAC,CAAA,IAAK,CAAC,EAAE,cAAc,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,IAAI;;;;;;;;;;;;sDAGlF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8IAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;;;;;;;;;;;;kCAMhB,8OAAC;wBAAI,WAAU;kCACZ,UACC,mBAAmB;sCACnB,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;oCAAY,WAAU;mCAAb;;;;;;;;;mCAGZ,gBAAgB,MAAM,GAAG,IAC3B,eAAe;sCACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,WAAU;sCAEV,cAAA,8OAAC,yLAAA,CAAA,kBAAe;0CACb,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAAiB,UAAU;;0DACpC,8OAAC,gKAAA,CAAA,UAAU;gDACT,MAAM,OAAO,IAAI;gDACjB,SAAS,OAAO,OAAO;gDACvB,SAAS,OAAO,cAAc,EAAE,IAAI,CAAA,IAAK,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,IAAI,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,KAAK,UAAU;gDAC/G,OAAO,OAAO,KAAK;gDACnB,WAAW,IAAM,cAAc,OAAO,EAAE;gDACxC,YAAY,IAAM,eAAe,OAAO,EAAE;gDAC1C,UAAU,IAAM,mBAAmB,OAAO,EAAE;gDAC5C,QAAQ,IAAM,WAAW,OAAO,EAAE;gDAClC,WAAW,IAAM,cAAc,OAAO,EAAE;;;;;;4CAGzC,mBAAmB,OAAO,EAAE,kBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAAC,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAAG,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAAG,MAAM;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAAG,WAAU;0DAChH,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAmC;;;;;;8EAClD,8OAAC;oEAAG,WAAU;8EACX,OAAO,cAAc,EAAE,IAAI,CAAC,GAAG,kBAC9B,8OAAC;;gFAAY,EAAE,IAAI;gFAAC;gFAAG,CAAC,OAAO,EAAE,OAAO,IAAI,IAAI,EAAE,OAAO,CAAC;;2EAAjD;;;;;;;;;;;;;;;;sEAIf,8OAAC;;8EACC,8OAAC;oEAAO,SAAS,IAAM,kBAAkB,OAAO,OAAO;oEAAG,WAAU;;wEAAqF,kBAAkB,OAAO,OAAO,iBAAG,8OAAC,8IAAA,CAAA,UAAO;;;;iGAAM,8OAAC,8IAAA,CAAA,SAAM;;;;;wEAAI;;;;;;;8EACrN,8OAAC;oEAAO,SAAS,IAAM,qBAAqB,OAAO,OAAO;oEAAG,WAAU;;sFAA+E,8OAAC,8IAAA,CAAA,iBAAc;;;;;wEAAG;;;;;;;;;;;;;;;;;;;;;;;;;uCA1BjK,OAAO,EAAE;;;;;;;;;;;;;;mCAoChC,cAAc;sCACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,UAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CACV,cAAc,gCAAgC;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;;0DAEV,8OAAC,8IAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUpD,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDAEV,cAAA,8OAAC,8IAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAIf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAA4C;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAA4C;;;;;;0DAGnF,8OAAC;gDACC,IAAG;gDACH,MAAM;gDACN,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAK5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS;4CACT,UAAU,CAAC,cAAc;4CACzB,WAAU;sDAET,eAAe,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU9C,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAO,SAAS,IAAM,mBAAmB;wCAAQ,WAAU;kDAC1D,cAAA,8OAAC,8IAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAC7B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,SAAS;4CAAoB,UAAU;4CAAY,WAAU;sDAClE,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU3C,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAO,SAAS,IAAM,mBAAmB;wCAAQ,WAAU;kDAC1D,cAAA,8OAAC,8IAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA4C;;;;;;0DAC7D,8OAAC;gDAAS,QAAQ;gDAAC,OAAO,eAAe;gDAAI,WAAU;;;;;;0DACvD,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAE5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,SAAS,IAAM,mBAAmB;4CAAQ,WAAU;sDAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASvI,mBAAmB,qCAClB,8OAAC,iKAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS;oBACP,mBAAmB;oBACnB,uBAAuB;oBACvB,oBAAoB;gBACtB;gBACA,eAAe;gBACf,YAAY;;;;;;YAKf,uBACC,8OAAC;gBAAI,WAAU;;oBACZ;kCACD,8OAAC;wBAAO,WAAU;wBAAO,SAAS,IAAM,SAAS;kCAAO,cAAA,8OAAC,8IAAA,CAAA,MAAG;;;;;;;;;;;;;;;;;;;;;;AAKtE", "debugId": null}}]}