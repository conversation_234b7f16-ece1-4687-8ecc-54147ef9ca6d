{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RbFBlz2F4Sj1xnBfLFiovwITVbxvWgVA/mPAaZqhI1M=", "__NEXT_PREVIEW_MODE_ID": "d4a0cfcb6ef00914911abc7340f9e809", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c172ed9b6c40853f47b1bec512cd968e47d39415be38b433262a263a2fd10b50", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b289f859c141bca10e2ab484018c1d1266ecebfc71a1e547763d416c1999566e"}}}, "sortedMiddleware": ["/"], "functions": {}}