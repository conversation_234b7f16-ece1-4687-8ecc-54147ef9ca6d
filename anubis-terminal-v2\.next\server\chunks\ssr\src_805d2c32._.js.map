{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/TimeFilter/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { FaCaretDown } from \"react-icons/fa\";\r\n\r\nexport default function TimeFilter({ timeOptions, selectedTime, handleTimeSelect }: {\r\n    timeOptions: string[],\r\n    selectedTime: string,\r\n    handleTimeSelect: (time: string) => void\r\n}) {\r\n\r\n\r\n    return (\r\n        <div className=\"rounded-sm border border-white/10 flex\">\r\n            {timeOptions.map((time, index) => (\r\n                <button\r\n                    key={index}\r\n                    onClick={() => handleTimeSelect(time)}\r\n                    className={`px-2.5 py-1 rounded-sm hover:bg-white/10 transition-colors text-xs hover:cursor-pointer ${time === selectedTime ? \"bg-white/10\" : \"\"\r\n                        }`}\r\n                >\r\n                    {time.toUpperCase()}\r\n                </button>\r\n            ))}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport function TimeFilterDropdown({ timeOptions, selectedTime, handleTimeSelect }: {\r\n    timeOptions: string[],\r\n    selectedTime: string,\r\n    handleTimeSelect: (time: string) => void\r\n}) {\r\n    const [isOpen, setIsOpen] = useState(false);\r\n    return (\r\n        <div className=\"relative flex items-center gap-x-1\">\r\n            <button\r\n                onClick={() => setIsOpen(!isOpen)}\r\n                className=\"px-2.5 py-1 rounded-sm border border-white/10 text-xs hover:bg-white/10 transition-colors flex items-center gap-x-1\"\r\n            >\r\n                {selectedTime.toUpperCase()}\r\n                <FaCaretDown className=\"text-white\" />\r\n            </button>\r\n            {isOpen && (\r\n                <div className=\"absolute top-8 left-0 mt-1 w-20 rounded-sm border border-white/10 bg-black z-10 flex flex-col gap-y-1\">\r\n                    {timeOptions.map((time, index) => (\r\n                        <motion.button\r\n                            key={index}\r\n                            onClick={() => {\r\n                                handleTimeSelect(time);\r\n                                setIsOpen(false);\r\n                            }}\r\n                            className={`px-2.5 py-1 hover:bg-white/10 transition-colors text-xs hover:cursor-pointer ${time === selectedTime ? \"bg-white/10\" : \"\"}`}\r\n                        >{time.toUpperCase()}\r\n                        </motion.button>\r\n                    ))}\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAHA;;;;;AAKe,SAAS,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAI/E;IAGG,qBACI,8OAAC;QAAI,WAAU;kBACV,YAAY,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;gBAEG,SAAS,IAAM,iBAAiB;gBAChC,WAAW,CAAC,wFAAwF,EAAE,SAAS,eAAe,gBAAgB,IACxI;0BAEL,KAAK,WAAW;eALZ;;;;;;;;;;AAUzB;AAEO,SAAS,mBAAmB,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAI/E;IACG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBACG,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;oBAET,aAAa,WAAW;kCACzB,8OAAC,8IAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;YAE1B,wBACG,8OAAC;gBAAI,WAAU;0BACV,YAAY,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBAEV,SAAS;4BACL,iBAAiB;4BACjB,UAAU;wBACd;wBACA,WAAW,CAAC,6EAA6E,EAAE,SAAS,eAAe,gBAAgB,IAAI;kCACzI,KAAK,WAAW;uBANT;;;;;;;;;;;;;;;;AAajC", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/TimeFilter/StatsFilter/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { IoFilter } from \"react-icons/io5\";\r\n\r\n\r\nexport default function StatsFilter() {\r\n\r\n    return (\r\n        <motion.button className=\"flex items-center gap-x-2 border border-white/30 px-2.5 py-1 rounded-sm  text-xs\"\r\n            whileTap={{\r\n                scale: 1.03\r\n            }}\r\n            whileHover={{\r\n                cursor: \"pointer\"\r\n            }}\r\n        >\r\n            <IoFilter className=\"text-white\" /> <span>Filter</span>\r\n        </motion.button>\r\n    )\r\n}\r\n\r\nfunction FilterDropdown() {\r\n    return (\r\n        <>\r\n\r\n\r\n        </>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAMe,SAAS;IAEpB,qBACI,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QAAC,WAAU;QACrB,UAAU;YACN,OAAO;QACX;QACA,YAAY;YACR,QAAQ;QACZ;;0BAEA,8OAAC,+IAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAe;0BAAC,8OAAC;0BAAK;;;;;;;;;;;;AAGtD;AAEA,SAAS;IACL,qBACI;AAKR", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/hooks/useGetMarketData.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from 'react';\r\nimport { getTrendingTokens, getNewTokenPairs, getGainerTokenPairs } from '@/services/api/flooz';\r\n\r\ninterface UseGetMarketDataOptions {\r\n    networks: string | undefined;\r\n    time: string;\r\n    pollingInterval?: number; // in ms\r\n}\r\n\r\ninterface UseGetMarketDataResult<TokensType = any, PairsType = any> {\r\n    trendingTokens: TokensType | null;\r\n    newPairs: PairsType | null;\r\n    gainers: PairsType | null;\r\n    loading: boolean;\r\n    isRefetching: boolean;\r\n    error: Error | null;\r\n    refetch: () => void;\r\n}\r\n\r\nexport function useGetMarketData({ networks, time, pollingInterval }: UseGetMarketDataOptions): UseGetMarketDataResult {\r\n    const [trendingTokens, setTrendingTokens] = useState<any | null>(null);\r\n    const [newPairs, setNewPairs] = useState<any | null>(null);\r\n    const [gainers, setGainers] = useState<any | null>(null);\r\n    const [loading, setLoading] = useState<boolean>(false);\r\n    const [isRefetching, setIsRefetching] = useState<boolean>(false);\r\n    const [error, setError] = useState<Error | null>(null);\r\n    const pollingRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n    const fetchData = useCallback(async (isPolling = false) => {\r\n        if (!isPolling) {\r\n            setLoading(true);\r\n        } else {\r\n            setIsRefetching(true);\r\n        }\r\n        setError(null);\r\n        try {\r\n            const [tokens, pairs, gainers] = await Promise.all([\r\n                getTrendingTokens(networks as string, time),\r\n                getNewTokenPairs(networks as string, time),\r\n                getGainerTokenPairs(networks as string, time)\r\n            ]);\r\n            setTrendingTokens(tokens);\r\n            setNewPairs(pairs);\r\n            setGainers(gainers);\r\n        } catch (err) {\r\n            setError(err as Error);\r\n        } finally {\r\n            if (!isPolling) {\r\n                setLoading(false);\r\n            } else {\r\n                setIsRefetching(false);\r\n            }\r\n        }\r\n    }, [networks, time]);\r\n\r\n    // Initial fetch and refetch on param change\r\n    useEffect(() => {\r\n        fetchData();\r\n        // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [fetchData]);\r\n\r\n    // Polling logic\r\n    useEffect(() => {\r\n        if (pollingInterval && pollingInterval > 0) {\r\n            pollingRef.current = setInterval(() => fetchData(true), pollingInterval);\r\n            return () => {\r\n                if (pollingRef.current) clearInterval(pollingRef.current);\r\n            };\r\n        }\r\n        return undefined;\r\n    }, [fetchData, pollingInterval]);\r\n\r\n    const refetch = useCallback(() => {\r\n        fetchData(true);\r\n    }, [fetchData]);\r\n\r\n    return {\r\n        trendingTokens,\r\n        newPairs,\r\n        gainers,\r\n        loading,\r\n        isRefetching,\r\n        error,\r\n        refetch,\r\n    };\r\n} "], "names": [], "mappings": ";;;AAAA;AACA;;;AAkBO,SAAS,iBAAiB,EAAE,QAAQ,EAAE,IAAI,EAAE,eAAe,EAA2B;IACzF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAEjD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,YAAY,KAAK;QAClD,IAAI,CAAC,WAAW;YACZ,WAAW;QACf,OAAO;YACH,gBAAgB;QACpB;QACA,SAAS;QACT,IAAI;YACA,MAAM,CAAC,QAAQ,OAAO,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC/C,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAAE,UAAoB;gBACtC,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,UAAoB;gBACrC,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD,EAAE,UAAoB;aAC3C;YACD,kBAAkB;YAClB,YAAY;YACZ,WAAW;QACf,EAAE,OAAO,KAAK;YACV,SAAS;QACb,SAAU;YACN,IAAI,CAAC,WAAW;gBACZ,WAAW;YACf,OAAO;gBACH,gBAAgB;YACpB;QACJ;IACJ,GAAG;QAAC;QAAU;KAAK;IAEnB,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN;IACA,uDAAuD;IAC3D,GAAG;QAAC;KAAU;IAEd,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,mBAAmB,kBAAkB,GAAG;YACxC,WAAW,OAAO,GAAG,YAAY,IAAM,UAAU,OAAO;YACxD,OAAO;gBACH,IAAI,WAAW,OAAO,EAAE,cAAc,WAAW,OAAO;YAC5D;QACJ;QACA,OAAO;IACX,GAAG;QAAC;QAAW;KAAgB;IAE/B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,UAAU;IACd,GAAG;QAAC;KAAU;IAEd,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Trade/Trending/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState } from \"react\";\r\nimport { useWeb3 } from \"@/contexts/Web3Context\";\r\nimport { TimeFilterDropdown } from \"@/components/Terminal/TimeFilter\";\r\nimport StatsFilter from \"@/components/Terminal/TimeFilter/StatsFilter\";\r\nimport { useGetMarketData } from \"@/hooks/useGetMarketData\";\r\nimport { getTimeAgo, getVolumeByTimeOption, getPriceChangeByTimeOption } from \"@/utils/data\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nconst timeOptions = [\"1h\", \"4h\", \"12h\", \"24h\"];\r\ntype TimeOption = string;\r\n\r\ninterface TrendingToken {\r\n    id: string;\r\n    details: {\r\n        name: string;\r\n        symbol: string;\r\n        address: string;\r\n        imageThumbUrl?: string;\r\n    };\r\n    activity: {\r\n        createdAt: number;\r\n        change1?: string;\r\n        change4?: string;\r\n        change12?: string;\r\n        change24?: string;\r\n        volume1?: string;\r\n        volume4?: string;\r\n        volume12?: string;\r\n        volume24?: string;\r\n        [key: string]: any;\r\n    };\r\n    marketData: {\r\n        marketCap: number;\r\n        [key: string]: any;\r\n    };\r\n}\r\n\r\nexport default function Trending() {\r\n    const { selectedChain } = useWeb3();\r\n    const { push } = useRouter();\r\n    const [selectedTime, setSelectedTime] = useState<TimeOption>(\"1h\");\r\n    const { trendingTokens, loading, error, refetch } = useGetMarketData({ networks: selectedChain?.slug, time: selectedTime, pollingInterval: 10000 });\r\n\r\n    const handleTimeSelect = (time: TimeOption) => setSelectedTime(time);\r\n\r\n    return (\r\n        <div className=\"w-full p-3 flex flex-col gap-2 overflow-hidden row-span-1 border-b border-white/20\">\r\n            <div className=\"flex items-center justify-start gap-x-2 mb-2\">\r\n                <h5 className=\"font-space-grotesk text-xs font-bold\">Trending</h5>\r\n                <TimeFilterDropdown timeOptions={timeOptions} selectedTime={selectedTime} handleTimeSelect={handleTimeSelect} />\r\n                <StatsFilter />\r\n            </div>\r\n            <div className=\"overflow-x-scroll scrollbar-hidden\">\r\n                <table className=\"w-full min-w-[420px] text-left text-xs border-separate border-spacing-y-1\">\r\n                    <thead>\r\n                        <tr className=\"text-white/60\">\r\n                            <th className=\"px-2 py-1 font-semibold\">Asset</th>\r\n                            <th className=\"px-2 py-1 font-semibold\">Age</th>\r\n                            <th className=\"px-2 py-1 font-semibold\">{selectedTime.toUpperCase()}</th>\r\n                            <th className=\"px-2 py-1 font-semibold\">Volume</th>\r\n                            <th className=\"px-2 py-1 font-semibold\">MCap</th>\r\n                        </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                        {loading && (\r\n                            <tr><td colSpan={5} className=\"text-center py-4 text-white/50\">Loading...</td></tr>\r\n                        )}\r\n                        {error && (\r\n                            <tr><td colSpan={5} className=\"text-center py-4 text-red-400\">Error loading data</td></tr>\r\n                        )}\r\n                        {trendingTokens && (trendingTokens as TrendingToken[]).slice(0, 8).map((token) => {\r\n                            // Fallbacks for missing activity fields\r\n                            const activity = {\r\n                                change1: \"0\", change4: \"0\", change12: \"0\", change24: \"0\",\r\n                                volume1: \"0\", volume4: \"0\", volume12: \"0\", volume24: \"0\",\r\n                                ...token.activity\r\n                            };\r\n                            const priceChange = getPriceChangeByTimeOption(activity, selectedTime);\r\n                            const volume = getVolumeByTimeOption(activity, selectedTime);\r\n                            return (\r\n                                <tr key={token.id} className=\"hover:bg-white/5 rounded transition cursor-pointer\" onClick={() => selectedChain?.slug && push(`/terminal/trade/${selectedChain.slug}/${token.details.address}`)}>\r\n                                    {/* Asset */}\r\n                                    <td className=\"px-2 py-1\">\r\n                                        <div className=\"flex items-center gap-2\">\r\n                                            {token.details.imageThumbUrl ? (\r\n                                                <div className=\"w-5 h-5 relative\">\r\n                                                    <img src={token.details.imageThumbUrl} alt={token.details.name} className=\"w-5 h-5 rounded-full\" />\r\n                                                    <img src={selectedChain?.logo} alt=\"\" className=\"w-2.5 h-2.5 absolute -bottom-1 -right-1\" />\r\n                                                </div>\r\n                                            ) : (\r\n                                                <div className=\"w-5 h-5 bg-gray-700 rounded-full flex items-center justify-center text-[10px] relative\">\r\n                                                    {token.details.name.charAt(0)}\r\n                                                </div>\r\n                                            )}\r\n                                            <span className=\"font-medium text-white truncate max-w-[70px]\">{token.details.name}</span>\r\n                                            <span className=\"text-white/40 text-[10px]\">{token.details.symbol}</span>\r\n                                        </div>\r\n                                    </td>\r\n                                    {/* Age */}\r\n                                    <td className=\"px-2 py-1 text-white/70\">{getTimeAgo(token.activity.createdAt)}</td>\r\n                                    {/* The Price Timing */}\r\n                                    <td className=\"px-2 py-1\">\r\n                                        <span className={priceChange > 0 ? \"text-green-500\" : priceChange < 0 ? \"text-red-500\" : \"text-white/70\"}>\r\n                                            {priceChange > 0 ? \"+\" : \"\"}\r\n                                            {priceChange.toFixed(1)}%\r\n                                        </span>\r\n                                    </td>\r\n                                    {/* Volume */}\r\n                                    <td className=\"px-2 py-1 font-mono text-white/80\">${volume.toLocaleString()}</td>\r\n                                    {/* Market Cap */}\r\n                                    <td className=\"px-2 py-1 text-white/70 font-mono\">${Number(token.marketData.marketCap).toLocaleString()}</td>\r\n                                </tr>\r\n                            );\r\n                        })}\r\n                        {trendingTokens && (trendingTokens as TrendingToken[]).length === 0 && !loading && (\r\n                            <tr><td colSpan={5} className=\"text-center py-4 text-white/50\">No trending tokens</td></tr>\r\n                        )}\r\n                    </tbody>\r\n                </table>\r\n            </div>\r\n        </div>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;AASA,MAAM,cAAc;IAAC;IAAM;IAAM;IAAO;CAAM;AA6B/B,SAAS;IACpB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IAC7D,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;QAAE,UAAU,eAAe;QAAM,MAAM;QAAc,iBAAiB;IAAM;IAEjJ,MAAM,mBAAmB,CAAC,OAAqB,gBAAgB;IAE/D,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,8OAAC,qJAAA,CAAA,qBAAkB;wBAAC,aAAa;wBAAa,cAAc;wBAAc,kBAAkB;;;;;;kCAC5F,8OAAC,oKAAA,CAAA,UAAW;;;;;;;;;;;0BAEhB,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBAAM,WAAU;;sCACb,8OAAC;sCACG,cAAA,8OAAC;gCAAG,WAAU;;kDACV,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAG,WAAU;kDAA2B,aAAa,WAAW;;;;;;kDACjE,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;;;;;;;;;;;;sCAGhD,8OAAC;;gCACI,yBACG,8OAAC;8CAAG,cAAA,8OAAC;wCAAG,SAAS;wCAAG,WAAU;kDAAiC;;;;;;;;;;;gCAElE,uBACG,8OAAC;8CAAG,cAAA,8OAAC;wCAAG,SAAS;wCAAG,WAAU;kDAAgC;;;;;;;;;;;gCAEjE,kBAAkB,AAAC,eAAmC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;oCACpE,wCAAwC;oCACxC,MAAM,WAAW;wCACb,SAAS;wCAAK,SAAS;wCAAK,UAAU;wCAAK,UAAU;wCACrD,SAAS;wCAAK,SAAS;wCAAK,UAAU;wCAAK,UAAU;wCACrD,GAAG,MAAM,QAAQ;oCACrB;oCACA,MAAM,cAAc,CAAA,GAAA,oHAAA,CAAA,6BAA0B,AAAD,EAAE,UAAU;oCACzD,MAAM,SAAS,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAAE,UAAU;oCAC/C,qBACI,8OAAC;wCAAkB,WAAU;wCAAqD,SAAS,IAAM,eAAe,QAAQ,KAAK,CAAC,gBAAgB,EAAE,cAAc,IAAI,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE;;0DAEzL,8OAAC;gDAAG,WAAU;0DACV,cAAA,8OAAC;oDAAI,WAAU;;wDACV,MAAM,OAAO,CAAC,aAAa,iBACxB,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAI,KAAK,MAAM,OAAO,CAAC,aAAa;oEAAE,KAAK,MAAM,OAAO,CAAC,IAAI;oEAAE,WAAU;;;;;;8EAC1E,8OAAC;oEAAI,KAAK,eAAe;oEAAM,KAAI;oEAAG,WAAU;;;;;;;;;;;iFAGpD,8OAAC;4DAAI,WAAU;sEACV,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;sEAGnC,8OAAC;4DAAK,WAAU;sEAAgD,MAAM,OAAO,CAAC,IAAI;;;;;;sEAClF,8OAAC;4DAAK,WAAU;sEAA6B,MAAM,OAAO,CAAC,MAAM;;;;;;;;;;;;;;;;;0DAIzE,8OAAC;gDAAG,WAAU;0DAA2B,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,QAAQ,CAAC,SAAS;;;;;;0DAE5E,8OAAC;gDAAG,WAAU;0DACV,cAAA,8OAAC;oDAAK,WAAW,cAAc,IAAI,mBAAmB,cAAc,IAAI,iBAAiB;;wDACpF,cAAc,IAAI,MAAM;wDACxB,YAAY,OAAO,CAAC;wDAAG;;;;;;;;;;;;0DAIhC,8OAAC;gDAAG,WAAU;;oDAAoC;oDAAE,OAAO,cAAc;;;;;;;0DAEzE,8OAAC;gDAAG,WAAU;;oDAAoC;oDAAE,OAAO,MAAM,UAAU,CAAC,SAAS,EAAE,cAAc;;;;;;;;uCA9BhG,MAAM,EAAE;;;;;gCAiCzB;gCACC,kBAAkB,AAAC,eAAmC,MAAM,KAAK,KAAK,CAAC,yBACpE,8OAAC;8CAAG,cAAA,8OAAC;wCAAG,SAAS;wCAAG,WAAU;kDAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3F", "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Trade/Trending/RecentlyViewed/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useWeb3 } from \"@/contexts/Web3Context\";\r\nimport { getTimeAgo } from \"@/utils/data\";\r\nimport React from \"react\";\r\n\r\nexport default function RecentlyViewed() {\r\n    const { recentlyViewed, selectedChain } = useWeb3();\r\n    const filtered = recentlyViewed.filter(token => token.chain === selectedChain?.slug);\r\n    return (\r\n        <div className=\"row-span-1 w-full p-3 min-h-[30%] overflow-auto scrollbar-hidden border-b border-white/20\">\r\n            <h5 className=\"font-space-grotesk text-xs font-bold\">Recently Viewed</h5>\r\n            <div className=\"overflow-x-scroll scrollbar-hidden\">\r\n                <table className=\"w-full min-w-[420px] text-left text-xs border-separate border-spacing-y-1\">\r\n                    <thead>\r\n                        <tr className=\"text-white/60\">\r\n                            <th className=\"px-2 py-1 font-semibold\">Asset</th>\r\n                            <th className=\"px-2 py-1 font-semibold\">Price</th>\r\n                            <th className=\"px-2 py-1 font-semibold\">Market Cap</th>\r\n                        </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                        {filtered.map((token, index) => {\r\n                            const tokenData = token.data?.results?.[0];\r\n                            if (!tokenData) return null;\r\n                            const { details, marketData, activity } = tokenData;\r\n                            return (\r\n                                <tr key={index} className=\"hover:bg-white/5 rounded transition cursor-pointer\">\r\n                                    <td className=\"px-2 py-1\">\r\n                                        <div className=\"flex items-center gap-2\">\r\n                                            {details?.imageThumbUrl ? (\r\n                                                <div className=\"w-5 h-5 relative\">\r\n                                                    <img src={details.imageThumbUrl} alt={details.name} className=\"w-5 h-5 rounded-full\" />\r\n                                                    <img src={selectedChain?.logo} alt=\"\" className=\"w-2.5 h-2.5 absolute -bottom-1 -right-1\" />\r\n                                                </div>\r\n                                            ) : (\r\n                                                <div className=\"w-5 h-5 bg-gray-700 rounded-full flex items-center justify-center text-[10px] relative\">\r\n                                                    {details?.name?.charAt(0) ?? '?'}\r\n                                                    <img src={selectedChain?.logo} alt=\"\" className=\"w-2.5 h-2.5 absolute -bottom-1 -right-1\" />\r\n                                                </div>\r\n                                            )}\r\n                                            <div className=\"flex flex-col\">\r\n                                                <span className=\"font-medium text-white truncate max-w-[70px]\">{details?.name ?? 'Unknown'}</span>\r\n                                                <span className=\"text-white/40 text-[10px]\">{details?.symbol ?? ''}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-2 py-1 text-white/70\">{marketData?.priceUSD ? `$${parseFloat(marketData.priceUSD).toLocaleString(undefined, { maximumFractionDigits: 6 })}` : '-'}</td>\r\n                                    <td className=\"px-2 py-1 text-white/70\">{marketData?.marketCap ? `$${parseFloat(marketData.marketCap).toLocaleString()}` : '-'}</td>\r\n                                </tr>\r\n                            );\r\n                        })}\r\n                    </tbody>\r\n                </table>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAKe,SAAS;IACpB,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,WAAW,eAAe,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK,KAAK,eAAe;IAC/E,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAG,WAAU;0BAAuC;;;;;;0BACrD,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBAAM,WAAU;;sCACb,8OAAC;sCACG,cAAA,8OAAC;gCAAG,WAAU;;kDACV,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;;;;;;;;;;;;sCAGhD,8OAAC;sCACI,SAAS,GAAG,CAAC,CAAC,OAAO;gCAClB,MAAM,YAAY,MAAM,IAAI,EAAE,SAAS,CAAC,EAAE;gCAC1C,IAAI,CAAC,WAAW,OAAO;gCACvB,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;gCAC1C,qBACI,8OAAC;oCAAe,WAAU;;sDACtB,8OAAC;4CAAG,WAAU;sDACV,cAAA,8OAAC;gDAAI,WAAU;;oDACV,SAAS,8BACN,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAI,KAAK,QAAQ,aAAa;gEAAE,KAAK,QAAQ,IAAI;gEAAE,WAAU;;;;;;0EAC9D,8OAAC;gEAAI,KAAK,eAAe;gEAAM,KAAI;gEAAG,WAAU;;;;;;;;;;;6EAGpD,8OAAC;wDAAI,WAAU;;4DACV,SAAS,MAAM,OAAO,MAAM;0EAC7B,8OAAC;gEAAI,KAAK,eAAe;gEAAM,KAAI;gEAAG,WAAU;;;;;;;;;;;;kEAGxD,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAK,WAAU;0EAAgD,SAAS,QAAQ;;;;;;0EACjF,8OAAC;gEAAK,WAAU;0EAA6B,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;sDAI5E,8OAAC;4CAAG,WAAU;sDAA2B,YAAY,WAAW,CAAC,CAAC,EAAE,WAAW,WAAW,QAAQ,EAAE,cAAc,CAAC,WAAW;gDAAE,uBAAuB;4CAAE,IAAI,GAAG;;;;;;sDAChK,8OAAC;4CAAG,WAAU;sDAA2B,YAAY,YAAY,CAAC,CAAC,EAAE,WAAW,WAAW,SAAS,EAAE,cAAc,IAAI,GAAG;;;;;;;mCArBtH;;;;;4BAwBjB;;;;;;;;;;;;;;;;;;;;;;;AAMxB", "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Trade/OpenPosition/index.tsx"], "sourcesContent": ["import { motion } from 'framer-motion';\r\n\r\nexport default function OpenPositions() {\r\n    return (\r\n        <div className=\"row-span-1 w-full p-3 min-h-[30%] overflow-auto scrollbar-hidden border-b border-white/20\">\r\n            <h5 className=\"font-space-grotesk text-xs font-bold\">Open Positions</h5>\r\n            <div className='flex flex-col gap-3 h-full justify-center items-center'>\r\n                <p className=\"text-white/50\">No open positions</p>\r\n                <motion.button\r\n                    className=\"font-orbitron font-semibold bg-white text-black px-8 py-3 rounded-md hover:shadow-lg hover:shadow-white/20 w-full text-xs\"\r\n                    whileHover={{\r\n                        boxShadow: \"0 0 15px rgba(255, 255, 255, 0.5)\",\r\n                        scale: 1.05\r\n                    }}\r\n                    whileTap={{\r\n                        backgroundColor: \"#f0f0f0\",\r\n                        scale: 1.03\r\n                    }}\r\n                >\r\n                    Trade or Deposit\r\n                </motion.button>\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACpB,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAG,WAAU;0BAAuC;;;;;;0BACrD,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACV,WAAU;wBACV,YAAY;4BACR,WAAW;4BACX,OAAO;wBACX;wBACA,UAAU;4BACN,iBAAiB;4BACjB,OAAO;wBACX;kCACH;;;;;;;;;;;;;;;;;;AAMjB", "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/services/api/dexscreener.ts"], "sourcesContent": ["import axios, { type AxiosError } from 'axios';\r\n\r\ninterface Token {\r\n    address: string;\r\n    name: string;\r\n    symbol: string;\r\n}\r\n\r\ninterface PairInfo {\r\n    imageUrl: string;\r\n    websites: { url: string }[];\r\n    socials: { platform: string; handle: string }[];\r\n}\r\n\r\ninterface Pair {\r\n    chainId: string;\r\n    dexId: string;\r\n    url: string;\r\n    pairAddress: string;\r\n    labels: string[];\r\n    baseToken: Token;\r\n    quoteToken: Token;\r\n    priceNative: string;\r\n    priceUsd: string;\r\n    txns: Record<string, { buys: number; sells: number }>;\r\n    volume: Record<string, number>;\r\n    priceChange: Record<string, number>;\r\n    liquidity: { usd: number; base: number; quote: number };\r\n    fdv: number;\r\n    marketCap: number;\r\n    pairCreatedAt: number;\r\n    info: PairInfo;\r\n    boosts: { active: number };\r\n}\r\n\r\ninterface DexScreenerResponse {\r\n    pairs: Pair[];\r\n}\r\n\r\nconst MAX_RETRIES = 3;\r\nconst INITIAL_DELAY = 1000;\r\n\r\nconst delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\r\n\r\nexport const getTokenPairs = async (\r\n    tokenAddress: `0x${string}`,\r\n    network: string,\r\n    retries = MAX_RETRIES\r\n): Promise<DexScreenerResponse> => {\r\n    // Get Pools First\r\n    const poolsEndpoint = `https://api.dexscreener.com/token-pairs/v1/${network}/${tokenAddress}`\r\n\r\n    try {\r\n        const response = await axios.get(poolsEndpoint);\r\n        if (!response) {\r\n            throw new Error(\"Failed to fetch pools\");\r\n        }\r\n\r\n        const url = `https://api.dexscreener.com/latest/dex/pairs/${network}/${response.data[0].pairAddress}`;\r\n\r\n        try {\r\n            const response = await axios.get<DexScreenerResponse>(url);\r\n            return response.data;\r\n        } catch (error) {\r\n            const axiosError = error as AxiosError;\r\n\r\n            if (axiosError.response?.status === 429 && retries > 0) {\r\n                const delayTime = INITIAL_DELAY * (MAX_RETRIES - retries + 1);\r\n                await delay(delayTime);\r\n                return getTokenPairs(tokenAddress, network, retries - 1);\r\n            }\r\n\r\n            console.error(\"Error fetching token pairs:\", error);\r\n            throw error;\r\n        }\r\n    } catch (error) {\r\n        console.error(\"Error fetching pools:\", error);\r\n        throw error;\r\n    }\r\n\r\n}\r\n\r\nexport const getTokenData = async (tokenAddress: `0x${string}`, network: string): Promise<Pair | null> => {\r\n    const url = `https://api.dexscreener.com/token-pairs/v1/${network}/${tokenAddress}`;\r\n    try {\r\n        const response = await axios.get(url);\r\n        return response.data[0];\r\n    } catch (error) {\r\n        console.error(\"Error fetching token data:\", error);\r\n        throw error;\r\n    }\r\n}"], "names": [], "mappings": ";;;;AAAA;;AAuCA,MAAM,cAAc;AACpB,MAAM,gBAAgB;AAEtB,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAElE,MAAM,gBAAgB,OACzB,cACA,SACA,UAAU,WAAW;IAErB,kBAAkB;IAClB,MAAM,gBAAgB,CAAC,2CAA2C,EAAE,QAAQ,CAAC,EAAE,cAAc;IAE7F,IAAI;QACA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC;QACjC,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,MAAM,CAAC,6CAA6C,EAAE,QAAQ,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE;QAErG,IAAI;YACA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAsB;YACtD,OAAO,SAAS,IAAI;QACxB,EAAE,OAAO,OAAO;YACZ,MAAM,aAAa;YAEnB,IAAI,WAAW,QAAQ,EAAE,WAAW,OAAO,UAAU,GAAG;gBACpD,MAAM,YAAY,gBAAgB,CAAC,cAAc,UAAU,CAAC;gBAC5D,MAAM,MAAM;gBACZ,OAAO,cAAc,cAAc,SAAS,UAAU;YAC1D;YAEA,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACV;IACJ,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACV;AAEJ;AAEO,MAAM,eAAe,OAAO,cAA6B;IAC5D,MAAM,MAAM,CAAC,2CAA2C,EAAE,QAAQ,CAAC,EAAE,cAAc;IACnF,IAAI;QACA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC;QACjC,OAAO,SAAS,IAAI,CAAC,EAAE;IAC3B,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACV;AACJ", "debugId": null}}, {"offset": {"line": 908, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/hooks/useTokenPairs.ts"], "sourcesContent": ["import axios, { AxiosError } from 'axios';\r\nimport { useState, useCallback, useEffect } from 'react';\r\nimport { getTokenPairs } from '../services/api/dexscreener';\r\nimport { getTokenData } from '@/services/api/flooz';\r\n\r\ntype UseTokenPairsReturn = {\r\n    data: any | null;\r\n    tokenData: any | null;\r\n    loading: boolean;\r\n    error: AxiosError | null;\r\n    fetchTokenPairs: () => Promise<void>;\r\n};\r\n\r\nexport const useTokenPairs = (tokenAddress: `0x${string}`, network: string): UseTokenPairsReturn => {\r\n    const [data, setData] = useState<any | null>(null);\r\n    const [tokenData, setTokenData] = useState<any | null>(null);\r\n    const [loading, setLoading] = useState<boolean>(true);\r\n    const [error, setError] = useState<AxiosError | null>(null);\r\n\r\n    const fetchTokenPairs = useCallback(async () => {\r\n        setLoading(true);\r\n        setError(null);\r\n\r\n        try {\r\n            const allResults = await Promise.all([\r\n                getTokenData(network, tokenAddress),\r\n                getTokenPairs(tokenAddress, network),\r\n            ]);\r\n            setData(allResults[1]);\r\n            setTokenData(allResults[0].results[0]);\r\n        } catch (error) {\r\n            setError(error as AxiosError);\r\n            setData(null);\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, [tokenAddress, network]);\r\n\r\n    useEffect(() => {\r\n        if (tokenAddress && network) {\r\n            fetchTokenPairs();\r\n        }\r\n    }, [fetchTokenPairs]);\r\n\r\n    return {\r\n        data,\r\n        tokenData,\r\n        loading,\r\n        error,\r\n        fetchTokenPairs,\r\n    };\r\n}; "], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAUO,MAAM,gBAAgB,CAAC,cAA6B;IACvD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAEtD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,WAAW;QACX,SAAS;QAET,IAAI;YACA,MAAM,aAAa,MAAM,QAAQ,GAAG,CAAC;gBACjC,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD,EAAE,SAAS;gBACtB,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD,EAAE,cAAc;aAC/B;YACD,QAAQ,UAAU,CAAC,EAAE;YACrB,aAAa,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;QACzC,EAAE,OAAO,OAAO;YACZ,SAAS;YACT,QAAQ;QACZ,SAAU;YACN,WAAW;QACf;IACJ,GAAG;QAAC;QAAc;KAAQ;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,gBAAgB,SAAS;YACzB;QACJ;IACJ,GAAG;QAAC;KAAgB;IAEpB,OAAO;QACH;QACA;QACA;QACA;QACA;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n    return twMerge(clsx(inputs));\r\n} "], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACtC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACxB", "debugId": null}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/ui/skeleton.tsx"], "sourcesContent": ["\"use client\"\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport function Skeleton({\r\n    className,\r\n    ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n    return (\r\n        <div\r\n            className={cn(\"animate-pulse rounded-md bg-muted\", className)}\r\n            {...props}\r\n        />\r\n    );\r\n} "], "names": [], "mappings": ";;;;AACA;AADA;;;AAGO,SAAS,SAAS,EACrB,SAAS,EACT,GAAG,OACgC;IACnC,qBACI,8OAAC;QACG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Trade/TokenPairData/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion } from 'framer-motion';\r\nimport { useTokenPairs } from '@/hooks/useTokenPairs';\r\nimport { FaStar } from \"react-icons/fa\";\r\nimport { useWeb3 } from \"@/contexts/Web3Context\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { chains, getTimeAgo, getShareLink } from '@/utils/data';\r\nimport { toast } from 'react-toastify';\r\n\r\n\r\nexport default function TokenPairData() {\r\n    const { chain, address } = useParams();\r\n    const { data, tokenData, loading, error, fetchTokenPairs } = useTokenPairs(address as `0x${string}`, chain as string);\r\n    const [favorite, setFavorite] = useState<boolean>(false);\r\n    const tokenChain = chains.find(c => c.slug === chain);\r\n\r\n    // Set page title based on token data\r\n    useEffect(() => {\r\n        if (data?.pair && tokenData?.marketData) {\r\n            const price = Number(data.pair.priceUsd || tokenData.marketData.priceUSD).toFixed(6);\r\n            const priceChange = data.pair.priceChange?.h24 || 0;\r\n            const symbol = data.pair.baseToken?.symbol || 'Token';\r\n            document.title = `${symbol}/${data.pair.quoteToken?.symbol} | $${price} | ${priceChange > 0 ? '↑' : '↓'} ${Math.abs(priceChange).toFixed(1)}% On ${tokenChain?.slug ? tokenChain.slug.charAt(0).toUpperCase() + tokenChain.slug.slice(1) : ''} / ${data?.pair?.dexId ? data.pair.dexId.charAt(0).toUpperCase() + data.pair.dexId.slice(1) : ''} | Anubis Terminal`;\r\n        }\r\n    }, [data, tokenData]);\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className='border-b border-white/20 flex justify-between gap-x-3 py-5 px-3'>\r\n                <Skeleton className=\"w-5 h-5 rounded-full\" />\r\n                <div className=\"flex gap-x-1 items-center\">\r\n                    <Skeleton className=\"w-5 h-5 rounded-full\" />\r\n                    <Skeleton className=\"h-4 w-20\" />\r\n                    <Skeleton className=\"h-3 w-10\" />\r\n                </div>\r\n                <Skeleton className=\"w-3 h-3 rounded-full\" />\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (error) {\r\n        return (\r\n            <div className='border-b border-white/20 flex justify-between gap-x-3 py-5 px-3 text-red-500'>\r\n                Error loading token data\r\n            </div>\r\n        );\r\n    }\r\n\r\n    console.log(\"Data\", data);\r\n    console.log(\"Token Data\", tokenData)\r\n    console.log(\"Token Chain\", tokenChain)\r\n\r\n    function handleCopyShareLink() {\r\n        navigator.clipboard.writeText(getShareLink(chain as string, address as string)).then(() => {\r\n            toast.success(\"Link Copied\");\r\n        }).catch((err) => {\r\n            toast.error(\"Failed to copy link\");\r\n            console.error(\"Failed to copy: \", err);\r\n        });\r\n    }\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-full\">\r\n            {/* Token Info Header - matches image */}\r\n            <div className=\"flex items-center justify-between px-4 py-3 bg-[#181818] border-b border-white/10\">\r\n                {/* Left: Token logo, name, pair, network, DEX, share */}\r\n                <div className=\"flex items-center gap-3 min-w-0\">\r\n                    {/* Favorite Star */}\r\n                    <motion.button\r\n                        whileTap={{ scale: 0.88 }}\r\n                        whileHover={{ cursor: \"pointer\", scale: 1.04 }}\r\n                        onClick={() => setFavorite(!favorite)}\r\n                        className=\"mr-1\"\r\n                    >\r\n                        <FaStar className={`${favorite ? \"text-yellow-400\" : \"text-white/30\"} text-lg`} />\r\n                    </motion.button>\r\n                    {/* Token Logo */}\r\n                    <img\r\n                        src={data?.pair?.info?.imageUrl || tokenData?.details?.imageLargeUrl}\r\n                        alt={data?.pair?.baseToken?.symbol}\r\n                        className=\"w-10 h-10 rounded-full border border-white/10 bg-black object-cover\"\r\n                    />\r\n                    {/* Name and Pair */}\r\n                    <div className=\"flex flex-col min-w-0\">\r\n                        <span className=\"font-orbitron font-bold text-white truncate text-base leading-tight\">\r\n                            {data?.pair?.baseToken?.symbol}/{data?.pair?.quoteToken?.symbol}\r\n                        </span>\r\n                        <div className=\"flex items-center gap-2 text-xs text-white/60 mt-0.5\">\r\n                            {/* Age */}\r\n                            <span>{getTimeAgo(tokenData?.activity?.createdAt || 0)}</span>\r\n                            {/* Network */}\r\n                            <span className=\"flex items-center gap-1\">\r\n                                <img src={tokenChain?.logo} alt=\"network\" className=\"w-4 h-4 inline-block\" />\r\n                                {tokenChain?.chain || data?.pair?.chainId}\r\n                            </span>\r\n                            {/* DEX */}\r\n                            <span className=\"flex items-center gap-1\">\r\n                                <img src=\"/icons/dex.svg\" alt=\"dex\" className=\"w-4 h-4 inline-block\" />\r\n                                {data?.pair?.dexId || 'DEX'}\r\n                            </span>\r\n                            {/* Share */}\r\n                            <button\r\n                                onClick={handleCopyShareLink}\r\n                                className=\"flex items-center gap-1 text-orange-400 hover:underline ml-2 cursor-pointer\"\r\n                            >\r\n                                <svg width=\"16\" height=\"16\" fill=\"none\" viewBox=\"0 0 16 16\"><path d=\"M12.667 10.667v1.333A1.333 1.333 0 0 1 11.333 13.333H4.667A1.333 1.333 0 0 1 3.333 12V5.333A1.333 1.333 0 0 1 4.667 4h1.333\" stroke=\"currentColor\" strokeWidth=\"1.2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" /><path d=\"M10.667 2.667h2.666v2.666\" stroke=\"currentColor\" strokeWidth=\"1.2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" /><path d=\"M7.333 8.667 13.333 2.667\" stroke=\"currentColor\" strokeWidth=\"1.2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" /></svg>\r\n                                <span className=\"font-medium\">Share</span>\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                {/* Right: Market Cap, Price, Liquidity, Taxes */}\r\n                <div className=\"flex items-center gap-8\">\r\n                    <div className=\"flex flex-col items-end\">\r\n                        <span className=\"text-xs text-white/50\">Market Cap</span>\r\n                        <span className=\"font-orbitron font-semibold text-lg text-white\">${Number(data?.pair?.marketCap || tokenData?.marketData?.marketCap).toLocaleString(undefined, { maximumFractionDigits: 2 })}M</span>\r\n                    </div>\r\n                    <div className=\"flex flex-col items-end\">\r\n                        <span className=\"text-xs text-white/50\">Price</span>\r\n                        <span className=\"font-orbitron font-semibold text-lg text-white\">${Number(data?.pair?.priceUsd || tokenData?.marketData?.priceUSD).toFixed(7)}</span>\r\n                    </div>\r\n                    <div className=\"flex flex-col items-end\">\r\n                        <span className=\"text-xs text-white/50\">Liquidity</span>\r\n                        <span className=\"font-orbitron font-semibold text-lg text-white\">${Number(data?.pair?.liquidity?.usd || tokenData?.marketData?.liquidity).toLocaleString(undefined, { maximumFractionDigits: 2 })}</span>\r\n                    </div>\r\n                    <div className=\"flex flex-col items-end\">\r\n                        <span className=\"text-xs text-white/50\">Taxes</span>\r\n                        <span className=\"font-orbitron font-semibold text-lg text-white\">0.0% <span className=\"text-white/40\">|</span> 0.0%</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Chart Placeholder */}\r\n            <div className=\"flex-1 flex flex-col bg-black/40 border-b border-white/10 justify-center items-center min-h-[260px]\">\r\n                <div className=\"w-full h-64 flex items-center justify-center\">\r\n                    <span className=\"text-white/40 text-lg\">[Chart Placeholder]</span>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Tabs */}\r\n            <div className=\"flex border-b border-white/10 bg-black/30\">\r\n                {['Trades', 'Open Orders', 'Order History', 'Liquidity', 'Holders', 'Top Traders'].map(tab => (\r\n                    <button\r\n                        key={tab}\r\n                        className={`px-4 py-2 text-sm font-medium text-white/80 hover:text-white focus:outline-none ${tab === 'Trades' ? 'border-b-2 border-green-500 text-white' : ''}`}\r\n                    >\r\n                        {tab}\r\n                    </button>\r\n                ))}\r\n            </div>\r\n\r\n            {/* Trades Table (Dummy Data) */}\r\n            <div className=\"flex-1 overflow-y-auto bg-black/20\">\r\n                <table className=\"min-w-full text-xs text-left text-white/80\">\r\n                    <thead className=\"bg-black/40 border-b border-white/10\">\r\n                        <tr>\r\n                            <th className=\"px-3 py-2 font-semibold\">Age</th>\r\n                            <th className=\"px-3 py-2 font-semibold\">Side</th>\r\n                            <th className=\"px-3 py-2 font-semibold\">MCap</th>\r\n                            <th className=\"px-3 py-2 font-semibold\">Amount</th>\r\n                            <th className=\"px-3 py-2 font-semibold\">Total USD</th>\r\n                            <th className=\"px-3 py-2 font-semibold\">Total USDT</th>\r\n                            <th className=\"px-3 py-2 font-semibold\">Maker</th>\r\n                        </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                        {[\r\n                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '195.2', totalUsd: '399.21', totalUsdt: '399.1553', maker: '186Cf5a' },\r\n                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '244.5', totalUsd: '500.07', totalUsdt: '500.000', maker: '7eeF25A9' },\r\n                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '760.9', totalUsd: '1,556.51', totalUsdt: '1,556.2861', maker: 'A1058569' },\r\n                            { age: '3s', side: 'Sell', mcap: '204.6M', amount: '37.8', totalUsd: '77.37', totalUsdt: '77.3585', maker: '67d1C5d8' },\r\n                            { age: '3s', side: 'Sell', mcap: '204.6M', amount: '1.01K', totalUsd: '2,059.90', totalUsdt: '2,059.6103', maker: '770Fa894' },\r\n                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '1.61K', totalUsd: '3,300.47', totalUsdt: '3,300.000', maker: '3243B0C4' },\r\n                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '801.8', totalUsd: '1,640.23', totalUsdt: '1,640.000', maker: '7F7355BF' },\r\n                        ].map((row, i) => (\r\n                            <tr key={i} className=\"border-b border-white/5 last:border-0\">\r\n                                <td className=\"px-3 py-2 whitespace-nowrap\">{row.age}</td>\r\n                                <td className={`px-3 py-2 whitespace-nowrap font-bold ${row.side === 'Buy' ? 'text-green-400' : 'text-red-400'}`}>{row.side}</td>\r\n                                <td className=\"px-3 py-2 whitespace-nowrap\">{row.mcap}</td>\r\n                                <td className=\"px-3 py-2 whitespace-nowrap\">{row.amount}</td>\r\n                                <td className=\"px-3 py-2 whitespace-nowrap\">{row.totalUsd}</td>\r\n                                <td className=\"px-3 py-2 whitespace-nowrap\">{row.totalUsdt}</td>\r\n                                <td className=\"px-3 py-2 whitespace-nowrap\">{row.maker}</td>\r\n                            </tr>\r\n                        ))}\r\n                    </tbody>\r\n                </table>\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AATA;;;;;;;;;;AAYe,SAAS;IACpB,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACnC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE,SAA0B;IACrG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAClD,MAAM,aAAa,oHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;IAE/C,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,MAAM,QAAQ,WAAW,YAAY;YACrC,MAAM,QAAQ,OAAO,KAAK,IAAI,CAAC,QAAQ,IAAI,UAAU,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC;YAClF,MAAM,cAAc,KAAK,IAAI,CAAC,WAAW,EAAE,OAAO;YAClD,MAAM,SAAS,KAAK,IAAI,CAAC,SAAS,EAAE,UAAU;YAC9C,SAAS,KAAK,GAAG,GAAG,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,EAAE,OAAO,IAAI,EAAE,MAAM,GAAG,EAAE,cAAc,IAAI,MAAM,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,aAAa,OAAO,CAAC,GAAG,KAAK,EAAE,YAAY,OAAO,WAAW,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,EAAE,MAAM,MAAM,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,kBAAkB,CAAC;QACtW;IACJ,GAAG;QAAC;QAAM;KAAU;IAEpB,IAAI,SAAS;QACT,qBACI,8OAAC;YAAI,WAAU;;8BACX,8OAAC,oIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;8BACpB,8OAAC;oBAAI,WAAU;;sCACX,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;8BAExB,8OAAC,oIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;;IAGhC;IAEA,IAAI,OAAO;QACP,qBACI,8OAAC;YAAI,WAAU;sBAA+E;;;;;;IAItG;IAEA,QAAQ,GAAG,CAAC,QAAQ;IACpB,QAAQ,GAAG,CAAC,cAAc;IAC1B,QAAQ,GAAG,CAAC,eAAe;IAE3B,SAAS;QACL,UAAU,SAAS,CAAC,SAAS,CAAC,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE,OAAiB,UAAoB,IAAI,CAAC;YACjF,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAClB,GAAG,KAAK,CAAC,CAAC;YACN,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,oBAAoB;QACtC;IACJ;IAEA,qBACI,8OAAC;QAAI,WAAU;;0BAEX,8OAAC;gBAAI,WAAU;;kCAEX,8OAAC;wBAAI,WAAU;;0CAEX,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACV,UAAU;oCAAE,OAAO;gCAAK;gCACxB,YAAY;oCAAE,QAAQ;oCAAW,OAAO;gCAAK;gCAC7C,SAAS,IAAM,YAAY,CAAC;gCAC5B,WAAU;0CAEV,cAAA,8OAAC,8IAAA,CAAA,SAAM;oCAAC,WAAW,GAAG,WAAW,oBAAoB,gBAAgB,QAAQ,CAAC;;;;;;;;;;;0CAGlF,8OAAC;gCACG,KAAK,MAAM,MAAM,MAAM,YAAY,WAAW,SAAS;gCACvD,KAAK,MAAM,MAAM,WAAW;gCAC5B,WAAU;;;;;;0CAGd,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAK,WAAU;;4CACX,MAAM,MAAM,WAAW;4CAAO;4CAAE,MAAM,MAAM,YAAY;;;;;;;kDAE7D,8OAAC;wCAAI,WAAU;;0DAEX,8OAAC;0DAAM,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,UAAU,aAAa;;;;;;0DAEpD,8OAAC;gDAAK,WAAU;;kEACZ,8OAAC;wDAAI,KAAK,YAAY;wDAAM,KAAI;wDAAU,WAAU;;;;;;oDACnD,YAAY,SAAS,MAAM,MAAM;;;;;;;0DAGtC,8OAAC;gDAAK,WAAU;;kEACZ,8OAAC;wDAAI,KAAI;wDAAiB,KAAI;wDAAM,WAAU;;;;;;oDAC7C,MAAM,MAAM,SAAS;;;;;;;0DAG1B,8OAAC;gDACG,SAAS;gDACT,WAAU;;kEAEV,8OAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,MAAK;wDAAO,SAAQ;;0EAAY,8OAAC;gEAAK,GAAE;gEAA8H,QAAO;gEAAe,aAAY;gEAAM,eAAc;gEAAQ,gBAAe;;;;;;0EAAU,8OAAC;gEAAK,GAAE;gEAA4B,QAAO;gEAAe,aAAY;gEAAM,eAAc;gEAAQ,gBAAe;;;;;;0EAAU,8OAAC;gEAAK,GAAE;gEAA4B,QAAO;gEAAe,aAAY;gEAAM,eAAc;gEAAQ,gBAAe;;;;;;;;;;;;kEACrgB,8OAAC;wDAAK,WAAU;kEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM9C,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;;4CAAiD;4CAAE,OAAO,MAAM,MAAM,aAAa,WAAW,YAAY,WAAW,cAAc,CAAC,WAAW;gDAAE,uBAAuB;4CAAE;4CAAG;;;;;;;;;;;;;0CAEjM,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;;4CAAiD;4CAAE,OAAO,MAAM,MAAM,YAAY,WAAW,YAAY,UAAU,OAAO,CAAC;;;;;;;;;;;;;0CAE/I,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;;4CAAiD;4CAAE,OAAO,MAAM,MAAM,WAAW,OAAO,WAAW,YAAY,WAAW,cAAc,CAAC,WAAW;gDAAE,uBAAuB;4CAAE;;;;;;;;;;;;;0CAEnM,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;;4CAAiD;0DAAK,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;4CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;0BAM1H,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;;;;;0BAKhD,8OAAC;gBAAI,WAAU;0BACV;oBAAC;oBAAU;oBAAe;oBAAiB;oBAAa;oBAAW;iBAAc,CAAC,GAAG,CAAC,CAAA,oBACnF,8OAAC;wBAEG,WAAW,CAAC,gFAAgF,EAAE,QAAQ,WAAW,2CAA2C,IAAI;kCAE/J;uBAHI;;;;;;;;;;0BASjB,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBAAM,WAAU;;sCACb,8OAAC;4BAAM,WAAU;sCACb,cAAA,8OAAC;;kDACG,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;;;;;;;;;;;;sCAGhD,8OAAC;sCACI;gCACG;oCAAE,KAAK;oCAAM,MAAM;oCAAO,MAAM;oCAAU,QAAQ;oCAAS,UAAU;oCAAU,WAAW;oCAAY,OAAO;gCAAU;gCACvH;oCAAE,KAAK;oCAAM,MAAM;oCAAO,MAAM;oCAAU,QAAQ;oCAAS,UAAU;oCAAU,WAAW;oCAAW,OAAO;gCAAW;gCACvH;oCAAE,KAAK;oCAAM,MAAM;oCAAO,MAAM;oCAAU,QAAQ;oCAAS,UAAU;oCAAY,WAAW;oCAAc,OAAO;gCAAW;gCAC5H;oCAAE,KAAK;oCAAM,MAAM;oCAAQ,MAAM;oCAAU,QAAQ;oCAAQ,UAAU;oCAAS,WAAW;oCAAW,OAAO;gCAAW;gCACtH;oCAAE,KAAK;oCAAM,MAAM;oCAAQ,MAAM;oCAAU,QAAQ;oCAAS,UAAU;oCAAY,WAAW;oCAAc,OAAO;gCAAW;gCAC7H;oCAAE,KAAK;oCAAM,MAAM;oCAAO,MAAM;oCAAU,QAAQ;oCAAS,UAAU;oCAAY,WAAW;oCAAa,OAAO;gCAAW;gCAC3H;oCAAE,KAAK;oCAAM,MAAM;oCAAO,MAAM;oCAAU,QAAQ;oCAAS,UAAU;oCAAY,WAAW;oCAAa,OAAO;gCAAW;6BAC9H,CAAC,GAAG,CAAC,CAAC,KAAK,kBACR,8OAAC;oCAAW,WAAU;;sDAClB,8OAAC;4CAAG,WAAU;sDAA+B,IAAI,GAAG;;;;;;sDACpD,8OAAC;4CAAG,WAAW,CAAC,sCAAsC,EAAE,IAAI,IAAI,KAAK,QAAQ,mBAAmB,gBAAgB;sDAAG,IAAI,IAAI;;;;;;sDAC3H,8OAAC;4CAAG,WAAU;sDAA+B,IAAI,IAAI;;;;;;sDACrD,8OAAC;4CAAG,WAAU;sDAA+B,IAAI,MAAM;;;;;;sDACvD,8OAAC;4CAAG,WAAU;sDAA+B,IAAI,QAAQ;;;;;;sDACzD,8OAAC;4CAAG,WAAU;sDAA+B,IAAI,SAAS;;;;;;sDAC1D,8OAAC;4CAAG,WAAU;sDAA+B,IAAI,KAAK;;;;;;;mCAPjD;;;;;;;;;;;;;;;;;;;;;;;;;;;AAerC", "debugId": null}}, {"offset": {"line": 1721, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/app/terminal/trade/%5Bchain%5D/%5Baddress%5D/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useParams } from \"next/navigation\"\r\nimport Trending from \"@/components/Terminal/Trade/Trending\";\r\nimport RecentlyViewed from \"@/components/Terminal/Trade/Trending/RecentlyViewed\";\r\nimport { useWeb3 } from \"@/contexts/Web3Context\";\r\nimport { useEffect } from \"react\";\r\nimport OpenPositions from \"@/components/Terminal/Trade/OpenPosition\";\r\nimport TokenPairData from \"@/components/Terminal/Trade/TokenPairData\";\r\n\r\nexport default function TradePage() {\r\n    const { chain, address } = useParams();\r\n    const { addRecentlyViewed } = useWeb3();\r\n\r\n    useEffect(() => {\r\n        // Only add if both are present and address is 0x-prefixed\r\n        if (typeof address === 'string' && typeof chain === 'string' && address.startsWith('0x')) {\r\n            addRecentlyViewed(address as `0x${string}`, chain);\r\n        }\r\n    }, [address, chain, addRecentlyViewed]);\r\n\r\n    useEffect(() => {\r\n        document.title = \"📊 Live Trading Dashboard - Real-Time Crypto Trading | Anubis Terminal\";\r\n    }, []);\r\n\r\n    return (\r\n        <section className=\"overflow-hidden grid grid-cols-12 h-[calc(100vh-80px)]\">\r\n            <div className=\"col-span-2 border-r border-white/10 w-full overflow-x-hidden h-full grid grid-rows-3\">\r\n                <Trending />\r\n                <RecentlyViewed />\r\n                <OpenPositions />\r\n            </div>\r\n            <div className=\"col-span-8 h-full\">\r\n                {/* Main content area */}\r\n                <TokenPairData />\r\n            </div>\r\n            <div className=\"col-span-2 border-l h-full flex flex-col bg-black/50 border border-white/10 rounded-md p-4 gap-4\">\r\n                {/* Buy/Sell Panel */}\r\n                <div className=\"mb-4\">\r\n                    <div className=\"flex gap-2 mb-3\">\r\n                        <button className=\"flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-2 rounded-md hover:bg-white/10 transition-colors\">Buy</button>\r\n                        <button className=\"flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-2 rounded-md hover:bg-white/10 transition-colors\">Sell</button>\r\n                        <button className=\"flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-2 rounded-md hover:bg-white/10 transition-colors\">Market</button>\r\n                    </div>\r\n                    <div className=\"mb-3\">\r\n                        <input className=\"w-full bg-black/40 border border-white/10 rounded-md px-3 py-2 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-green-500 transition\" placeholder=\"Total\" />\r\n                        <div className=\"flex gap-2 mt-2\">\r\n                            <button className=\"flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors\">$50</button>\r\n                            <button className=\"flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors\">$100</button>\r\n                            <button className=\"flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors\">$500</button>\r\n                            <button className=\"flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors\">$1,000</button>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-2 mb-3\">\r\n                        <input type=\"checkbox\" className=\"accent-green-500\" id=\"exit-strategy\" />\r\n                        <label htmlFor=\"exit-strategy\" className=\"text-xs text-white/80\">Exit Strategy</label>\r\n                    </div>\r\n                    <button className=\"w-full font-orbitron font-semibold bg-white text-black py-2 rounded-md shadow hover:shadow-white/20 transition-all text-base mt-2\">Instant trade</button>\r\n                </div>\r\n                {/* Stats Panel */}\r\n                <div className=\"border border-white/10 rounded-md p-3 text-xs text-white/80 bg-black/40 mb-4\">\r\n                    <div className=\"flex justify-between mb-1\">\r\n                        <span>Gas</span>\r\n                        <span>~0.00 ZK</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between mb-1\">\r\n                        <span>Buy</span>\r\n                        <span>--</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between mb-1\">\r\n                        <span>Sell</span>\r\n                        <span>--</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between mb-1\">\r\n                        <span>Tips</span>\r\n                        <span>0.000Ξ</span>\r\n                    </div>\r\n                </div>\r\n                {/* Data & Security Warnings */}\r\n                <div className=\"border border-white/10 rounded-md p-3 text-xs text-white/80 bg-black/40 flex-1 overflow-y-auto\">\r\n                    <div className=\"mb-2 flex items-center gap-2\">\r\n                        <span className=\"text-yellow-400 font-bold\">Data & Security</span>\r\n                        <span className=\"text-red-500\">2 warnings</span>\r\n                    </div>\r\n                    <ul className=\"list-disc ml-5 space-y-1\">\r\n                        <li>Snipers: 0/0 (0.00%)</li>\r\n                        <li>First buyers: 0/0 (0.00%)</li>\r\n                        <li>Dev holding: --</li>\r\n                        <li>Top 10 Holders: 93.34%</li>\r\n                        <li>Can't Sell: --</li>\r\n                    </ul>\r\n                </div>\r\n            </div>\r\n        </section>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;AASe,SAAS;IACpB,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACnC,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,0DAA0D;QAC1D,IAAI,OAAO,YAAY,YAAY,OAAO,UAAU,YAAY,QAAQ,UAAU,CAAC,OAAO;YACtF,kBAAkB,SAA0B;QAChD;IACJ,GAAG;QAAC;QAAS;QAAO;KAAkB;IAEtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,SAAS,KAAK,GAAG;IACrB,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;;0BACf,8OAAC;gBAAI,WAAU;;kCACX,8OAAC,4JAAA,CAAA,UAAQ;;;;;kCACT,8OAAC,8KAAA,CAAA,UAAc;;;;;kCACf,8OAAC,gKAAA,CAAA,UAAa;;;;;;;;;;;0BAElB,8OAAC;gBAAI,WAAU;0BAEX,cAAA,8OAAC,iKAAA,CAAA,UAAa;;;;;;;;;;0BAElB,8OAAC;gBAAI,WAAU;;kCAEX,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAO,WAAU;kDAAsI;;;;;;kDACxJ,8OAAC;wCAAO,WAAU;kDAAsI;;;;;;kDACxJ,8OAAC;wCAAO,WAAU;kDAAsI;;;;;;;;;;;;0CAE5J,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAM,WAAU;wCAAiK,aAAY;;;;;;kDAC9L,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAO,WAAU;0DAAwI;;;;;;0DAC1J,8OAAC;gDAAO,WAAU;0DAAwI;;;;;;0DAC1J,8OAAC;gDAAO,WAAU;0DAAwI;;;;;;0DAC1J,8OAAC;gDAAO,WAAU;0DAAwI;;;;;;;;;;;;;;;;;;0CAGlK,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAM,MAAK;wCAAW,WAAU;wCAAmB,IAAG;;;;;;kDACvD,8OAAC;wCAAM,SAAQ;wCAAgB,WAAU;kDAAwB;;;;;;;;;;;;0CAErE,8OAAC;gCAAO,WAAU;0CAAoI;;;;;;;;;;;;kCAG1J,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;;;;;;;0CAEV,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;;;;;;;0CAEV,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;;;;;;;0CAEV,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAId,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAK,WAAU;kDAA4B;;;;;;kDAC5C,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEnC,8OAAC;gCAAG,WAAU;;kDACV,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}]}